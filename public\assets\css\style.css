body {
    font-family: '<PERSON>pin<PERSON>', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    min-height: 100vh;
}

.prayer-card {
    transition: all 0.3s ease;
}

.prayer-card:hover {
    transform: translateY(-5px);
}

.next-prayer {
    background: linear-gradient(135deg, #43cea2 0%, #185a9d 100%);
}

.mosque-dome {
    filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
}

.compass-container {
    position: relative;
    width: 150px;
    height: 150px;
    margin: 0 auto;
}

.compass {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    position: relative;
    transition: transform 1s ease;
}

.compass-marking {
    position: absolute;
    width: 2px;
    height: 20px;
    background: white;
    left: 50%;
    transform-origin: bottom;
    border-radius: 1px;
}

.compass-marking:nth-child(1) {
    top: 5px;
    transform: translateX(-50%);
}

.compass-marking:nth-child(2) {
    top: 50%;
    right: 5px;
    width: 20px;
    height: 2px;
    transform: translateY(-50%);
}

.compass-marking:nth-child(3) {
    bottom: 5px;
    transform: translateX(-50%);
}

.compass-marking:nth-child(4) {
    top: 50%;
    left: 5px;
    width: 20px;
    height: 2px;
    transform: translateY(-50%);
}

.compass-needle {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 4px;
    height: 60px;
    background: linear-gradient(to top, #ff4444 0%, #ff4444 50%, #ffffff 50%, #ffffff 100%);
    transform: translate(-50%, -50%);
    border-radius: 2px;
    transform-origin: center;
    transition: transform 0.5s ease;
}

.compass-center {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    background: #FFD700;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    border: 2px solid white;
}

/* Navigation styles */
nav {
    backdrop-filter: blur(15px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.nav-link {
    position: relative;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    color: #FFD700;
}

.nav-link.active {
    color: #FFD700 !important;
    font-weight: 600;
    border-bottom: 2px solid #FFD700;
    padding-bottom: 0.25rem;
}

/* Enhanced Navigation */
nav .container {
    position: relative;
}

nav .flex:last-child {
    gap: 1.5rem;
}

/* Form styles */
select, input {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: white !important;
}

select option {
    background: #2a5298;
    color: white;
}

/* Button styles */
button {
    transition: all 0.3s ease;
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Animation for prayer cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.prayer-card {
    animation: fadeInUp 0.6s ease-out;
}

.prayer-card:nth-child(1) { animation-delay: 0.1s; }
.prayer-card:nth-child(2) { animation-delay: 0.2s; }
.prayer-card:nth-child(3) { animation-delay: 0.3s; }
.prayer-card:nth-child(4) { animation-delay: 0.4s; }
.prayer-card:nth-child(5) { animation-delay: 0.5s; }

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    nav .flex {
        flex-direction: column;
        gap: 1rem;
    }

    .compass-container {
        width: 120px;
        height: 120px;
    }
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

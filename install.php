<?php

/**
 * Installation Script for Prayer Times App
 *
 * This script helps set up the application for the first time.
 * Usage: php install.php
 */

echo "=== Prayer Times App Installation ===\n\n";

// Check PHP version
if (version_compare(PHP_VERSION, '7.3.0', '<')) {
    echo "❌ Error: PHP 7.3 or higher is required. Current version: " . PHP_VERSION . "\n";
    exit(1);
}
echo "✅ PHP version: " . PHP_VERSION . "\n";

// Check if Composer is available
$composerPath = null;
$composerCommands = ['composer', 'composer.phar'];

foreach ($composerCommands as $cmd) {
    $output = [];
    $returnCode = 0;
    exec("$cmd --version 2>/dev/null", $output, $returnCode);
    if ($returnCode === 0) {
        $composerPath = $cmd;
        break;
    }
}

if (!$composerPath) {
    echo "❌ Error: Composer not found. Please install Composer first.\n";
    echo "   Visit: https://getcomposer.org/download/\n";
    exit(1);
}
echo "✅ Composer found: $composerPath\n";

// Install dependencies
echo "\n📦 Installing dependencies...\n";
$output = [];
$returnCode = 0;
exec("$composerPath install --no-dev 2>&1", $output, $returnCode);

if ($returnCode !== 0) {
    echo "❌ Error installing dependencies:\n";
    echo implode("\n", $output) . "\n";
    exit(1);
}
echo "✅ Dependencies installed successfully\n";

// Create .env file if it doesn't exist
if (!file_exists('.env')) {
    if (file_exists('.env.example')) {
        copy('.env.example', '.env');
        echo "✅ Created .env file from .env.example\n";
    } else {
        echo "⚠️  Warning: .env.example not found\n";
    }
} else {
    echo "✅ .env file already exists\n";
}

// Create logs directory
$logsDir = __DIR__ . '/logs';
if (!is_dir($logsDir)) {
    mkdir($logsDir, 0755, true);
    echo "✅ Created logs directory\n";
} else {
    echo "✅ Logs directory already exists\n";
}

// Create cache directory if needed
$cacheDir = __DIR__ . '/cache';
if (!is_dir($cacheDir)) {
    mkdir($cacheDir, 0755, true);
    echo "✅ Created cache directory\n";
}

// Check required extensions
$requiredExtensions = ['json', 'curl', 'mbstring'];
$missingExtensions = [];

foreach ($requiredExtensions as $ext) {
    if (!extension_loaded($ext)) {
        $missingExtensions[] = $ext;
    }
}

if (!empty($missingExtensions)) {
    echo "❌ Missing required PHP extensions: " . implode(', ', $missingExtensions) . "\n";
    exit(1);
}
echo "✅ All required PHP extensions are loaded\n";

echo "\n🎉 Installation completed successfully!\n\n";
echo "Next steps:\n";
echo "1. Configure your .env file with your settings\n";
echo "2. Set up your web server to point to the 'public' directory\n";
echo "3. Or run the development server: php serve.php\n";
echo "4. Visit your application in the browser\n\n";
echo "For more information, see README.md\n";

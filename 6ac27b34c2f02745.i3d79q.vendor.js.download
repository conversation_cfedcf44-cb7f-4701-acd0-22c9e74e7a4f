/*! For license information please see 6ac27b34c2f02745.i3d79q.vendor.js.LICENSE.txt */
"use strict";(self.webpackChunk_canva_web=self.webpackChunk_canva_web||[]).push([[869588],{519427:(e,t,n)=>{n.r(t),n.d(t,{$mobx:()=>W,FlowCancellationError:()=>bn,ObservableMap:()=>wr,ObservableSet:()=>jr,Reaction:()=>Ct,_allowStateChanges:()=>Xe,_allowStateChangesInsideComputed:()=>Xt,_allowStateReadsEnd:()=>dt,_allowStateReadsStart:()=>vt,_autoAction:()=>Ft,_endAction:()=>Fe,_getAdministration:()=>Yr,_getGlobalState:()=>wt,_interceptReads:()=>kn,_isComputingDerivation:()=>st,_resetGlobalState:()=>St,_startAction:()=>ze,action:()=>zt,autorun:()=>Jt,comparer:()=>Y,computed:()=>Ue,configure:()=>ln,createAtom:()=>X,defineProperty:()=>Un,entries:()=>Dn,extendObservable:()=>fn,flow:()=>On,flowResult:()=>Sn,get:()=>In,getAtom:()=>Xr,getDebugName:()=>Jr,getDependencyTree:()=>hn,getObserverTree:()=>vn,has:()=>Bn,intercept:()=>jn,isAction:()=>Yt,isBoxedObservable:()=>Ze,isComputed:()=>xn,isComputedProp:()=>Pn,isFlow:()=>An,isFlowCancellationError:()=>yn,isObservable:()=>Cn,isObservableArray:()=>yr,isObservableMap:()=>Sr,isObservableObject:()=>Dr,isObservableProp:()=>Tn,isObservableSet:()=>Er,keys:()=>Vn,makeAutoObservable:()=>ar,makeObservable:()=>ir,observable:()=>Ne,observe:()=>Gn,onBecomeObserved:()=>rn,onBecomeUnobserved:()=>on,onReactionError:()=>Tt,override:()=>ee,ownKeys:()=>Kn,reaction:()=>en,remove:()=>Ln,runInAction:()=>Xt,set:()=>Nn,spy:()=>Bt,toJS:()=>Hn,trace:()=>Wn,transaction:()=>zn,untracked:()=>ft,values:()=>Mn,when:()=>Fn});function r(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw new Error("number"==typeof e?"[MobX] minified error nr: "+e+(n.length?" "+n.map(String).join(","):"")+". Find the full error at: https://github.com/mobxjs/mobx/blob/main/packages/mobx/src/errors.ts":"[MobX] "+e)}var i={};function o(){return"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==n.g?n.g:"undefined"!=typeof self?self:i}var a=Object.assign,s=Object.getOwnPropertyDescriptor,u=Object.defineProperty,c=Object.prototype,l=[];Object.freeze(l);var f={};Object.freeze(f);var h="undefined"!=typeof Proxy,_=Object.toString();function v(){h||r("Proxy not available")}function d(e){var t=!1;return function(){if(!t)return t=!0,e.apply(this,arguments)}}var p=function(){};function b(e){return"function"==typeof e}function y(e){switch(typeof e){case"string":case"symbol":case"number":return!0}return!1}function g(e){return null!==e&&"object"==typeof e}function m(e){if(!g(e))return!1;var t=Object.getPrototypeOf(e);if(null==t)return!0;var n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n.toString()===_}function O(e){var t=null==e?void 0:e.constructor;return!!t&&("GeneratorFunction"===t.name||"GeneratorFunction"===t.displayName)}function w(e,t,n){u(e,t,{enumerable:!1,writable:!0,configurable:!0,value:n})}function S(e,t,n){u(e,t,{enumerable:!1,writable:!1,configurable:!0,value:n})}function A(e,t){var n="isMobX"+e;return t.prototype[n]=!0,function(e){return g(e)&&!0===e[n]}}function k(e){return null!=e&&"[object Map]"===Object.prototype.toString.call(e)}function j(e){return null!=e&&"[object Set]"===Object.prototype.toString.call(e)}var E=void 0!==Object.getOwnPropertySymbols;var x="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:E?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames;function P(e){return null===e?null:"object"==typeof e?""+e:e}function R(e,t){return c.hasOwnProperty.call(e,t)}var C=Object.getOwnPropertyDescriptors||function(e){var t={};return x(e).forEach((function(n){t[n]=s(e,n)})),t};function T(e,t){return!!(e&t)}function V(e,t,n){return n?e|=t:e&=~t,e}function M(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function D(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,K(r.key),r)}}function N(e,t,n){return t&&D(e.prototype,t),n&&D(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function L(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(n)return(n=n.call(e)).next.bind(n);if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return M(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?M(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0;return function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function B(){return B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},B.apply(null,arguments)}function I(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,U(e,t)}function U(e,t){return U=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},U(e,t)}function K(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}var G=Symbol("mobx-stored-annotations");function q(e){return Object.assign((function(t,n){if(H(n))return e.decorate_20223_(t,n);$(t,n,e)}),e)}function $(e,t,n){R(e,G)||w(e,G,B({},e[G])),function(e){return e.annotationType_===Z}(n)||(e[G][t]=n)}function H(e){return"object"==typeof e&&"string"==typeof e.kind}var W=Symbol("mobx administration"),z=function(){function e(e){void 0===e&&(e="Atom"),this.name_=void 0,this.flags_=0,this.observers_=new Set,this.lastAccessedBy_=0,this.lowestObserverState_=tt.NOT_TRACKING_,this.onBOL=void 0,this.onBUOL=void 0,this.name_=e}var t=e.prototype;return t.onBO=function(){this.onBOL&&this.onBOL.forEach((function(e){return e()}))},t.onBUO=function(){this.onBUOL&&this.onBUOL.forEach((function(e){return e()}))},t.reportObserved=function(){return Pt(this)},t.reportChanged=function(){Et(),Rt(this),xt()},t.toString=function(){return this.name_},N(e,[{key:"isBeingObserved",get:function(){return T(this.flags_,e.isBeingObservedMask_)},set:function(t){this.flags_=V(this.flags_,e.isBeingObservedMask_,t)}},{key:"isPendingUnobservation",get:function(){return T(this.flags_,e.isPendingUnobservationMask_)},set:function(t){this.flags_=V(this.flags_,e.isPendingUnobservationMask_,t)}},{key:"diffValue",get:function(){return T(this.flags_,e.diffValueMask_)?1:0},set:function(t){this.flags_=V(this.flags_,e.diffValueMask_,1===t)}}])}();z.isBeingObservedMask_=1,z.isPendingUnobservationMask_=2,z.diffValueMask_=4;var F=A("Atom",z);function X(e,t,n){void 0===t&&(t=p),void 0===n&&(n=p);var r=new z(e);return t!==p&&rn(r,t),n!==p&&on(r,n),r}var Y={identity:function(e,t){return e===t},structural:function(e,t){return ti(e,t)},default:function(e,t){return Object.is?Object.is(e,t):e===t?0!==e||1/e==1/t:e!=e&&t!=t},shallow:function(e,t){return ti(e,t,1)}};function J(e,t,n){return Cn(e)?e:Array.isArray(e)?Ne.array(e,{name:n}):m(e)?Ne.object(e,void 0,{name:n}):k(e)?Ne.map(e,{name:n}):j(e)?Ne.set(e,{name:n}):"function"!=typeof e||Yt(e)||An(e)?e:O(e)?On(e):Ft(n,e)}function Q(e){return e}var Z="override",ee=q({annotationType_:Z,make_:function(e,t){0;0;return 0},extend_:function(e,t,n,i){r("'"+this.annotationType_+"' can only be used with 'makeObservable'")},decorate_20223_:function(e,t){console.warn("'"+this.annotationType_+"' cannot be used with decorators - this is a no-op")}});function te(e,t){return{annotationType_:e,options_:t,make_:ne,extend_:re,decorate_20223_:ie}}function ne(e,t,n,r){var i;if(null!=(i=this.options_)&&i.bound)return null===this.extend_(e,t,n,!1)?0:1;if(r===e.target_)return null===this.extend_(e,t,n,!1)?0:2;if(Yt(n.value))return 1;var o=oe(e,this,t,n,!1);return u(r,t,o),2}function re(e,t,n,r){var i=oe(e,this,t,n);return e.defineProperty_(t,i,r)}function ie(e,t){var n,i=t.kind,o=t.name,a=t.addInitializer,s=this,u=function(e){var t,n,r,i;return He(null!=(t=null==(n=s.options_)?void 0:n.name)?t:o.toString(),e,null!=(r=null==(i=s.options_)?void 0:i.autoAction)&&r)};return"field"==i?function(e){var t,n=e;return Yt(n)||(n=u(n)),null!=(t=s.options_)&&t.bound&&((n=n.bind(this)).isMobxAction=!0),n}:"method"==i?(Yt(e)||(e=u(e)),null!=(n=this.options_)&&n.bound&&a((function(){var e=this,t=e[o].bind(e);t.isMobxAction=!0,e[o]=t})),e):void r("Cannot apply '"+s.annotationType_+"' to '"+String(o)+"' (kind: "+i+"):\n'"+s.annotationType_+"' can only be used on properties with a function value.")}function oe(e,t,n,r,i){var o,a,s,u,c,l,f,h;void 0===i&&(i=Ot.safeDescriptors),h=r,t.annotationType_,h.value;var _,v=r.value;null!=(o=t.options_)&&o.bound&&(v=v.bind(null!=(_=e.proxy_)?_:e.target_));return{value:He(null!=(a=null==(s=t.options_)?void 0:s.name)?a:n.toString(),v,null!=(u=null==(c=t.options_)?void 0:c.autoAction)&&u,null!=(l=t.options_)&&l.bound?null!=(f=e.proxy_)?f:e.target_:void 0),configurable:!i||e.isPlainObject_,enumerable:!1,writable:!i}}function ae(e,t){return{annotationType_:e,options_:t,make_:se,extend_:ue,decorate_20223_:ce}}function se(e,t,n,r){var i;if(r===e.target_)return null===this.extend_(e,t,n,!1)?0:2;if(null!=(i=this.options_)&&i.bound&&(!R(e.target_,t)||!An(e.target_[t]))&&null===this.extend_(e,t,n,!1))return 0;if(An(n.value))return 1;var o=le(e,this,t,n,!1,!1);return u(r,t,o),2}function ue(e,t,n,r){var i,o=le(e,this,t,n,null==(i=this.options_)?void 0:i.bound);return e.defineProperty_(t,o,r)}function ce(e,t){var n;var r=t.name,i=t.addInitializer;return An(e)||(e=On(e)),null!=(n=this.options_)&&n.bound&&i((function(){var e=this,t=e[r].bind(e);t.isMobXFlow=!0,e[r]=t})),e}function le(e,t,n,r,i,o){var a;void 0===o&&(o=Ot.safeDescriptors),a=r,t.annotationType_,a.value;var s,u=r.value;(An(u)||(u=On(u)),i)&&((u=u.bind(null!=(s=e.proxy_)?s:e.target_)).isMobXFlow=!0);return{value:u,configurable:!o||e.isPlainObject_,enumerable:!1,writable:!o}}function fe(e,t){return{annotationType_:e,options_:t,make_:he,extend_:_e,decorate_20223_:ve}}function he(e,t,n){return null===this.extend_(e,t,n,!1)?0:1}function _e(e,t,n,r){return function(e,t,n,r){t.annotationType_,r.get;0}(0,this,0,n),e.defineComputedProperty_(t,B({},this.options_,{get:n.get,set:n.set}),r)}function ve(e,t){var n=this,r=t.name;return(0,t.addInitializer)((function(){var t=Tr(this)[W],i=B({},n.options_,{get:e,context:this});i.name||(i.name="ObservableObject."+r.toString()),t.values_.set(r,new et(i))})),function(){return this[W].getObservablePropValue_(r)}}function de(e,t){return{annotationType_:e,options_:t,make_:pe,extend_:be,decorate_20223_:ye}}function pe(e,t,n){return null===this.extend_(e,t,n,!1)?0:1}function be(e,t,n,r){var i,o;return function(e,t,n,r){t.annotationType_;0}(0,this),e.defineObservableProperty_(t,n.value,null!=(i=null==(o=this.options_)?void 0:o.enhancer)?i:J,r)}function ye(e,t){var n=this,r=t.kind,i=t.name,o=new WeakSet;function a(e,t){var r,a,s=Tr(e)[W],u=new Qe(t,null!=(r=null==(a=n.options_)?void 0:a.enhancer)?r:J,"ObservableObject."+i.toString(),!1);s.values_.set(i,u),o.add(e)}if("accessor"==r)return{get:function(){return o.has(this)||a(this,e.get.call(this)),this[W].getObservablePropValue_(i)},set:function(e){return o.has(this)||a(this,e),this[W].setObservablePropValue_(i,e)},init:function(e){return o.has(this)||a(this,e),e}}}var ge="true",me=Oe();function Oe(e){return{annotationType_:ge,options_:e,make_:we,extend_:Se,decorate_20223_:Ae}}function we(e,t,n,r){var i,o,a,s;if(n.get)return Ue.make_(e,t,n,r);if(n.set){var c=He(t.toString(),n.set);return r===e.target_?null===e.defineProperty_(t,{configurable:!Ot.safeDescriptors||e.isPlainObject_,set:c})?0:2:(u(r,t,{configurable:!0,set:c}),2)}if(r!==e.target_&&"function"==typeof n.value)return O(n.value)?(null!=(s=this.options_)&&s.autoBind?On.bound:On).make_(e,t,n,r):(null!=(a=this.options_)&&a.autoBind?Ft.bound:Ft).make_(e,t,n,r);var l,f=!1===(null==(i=this.options_)?void 0:i.deep)?Ne.ref:Ne;"function"==typeof n.value&&null!=(o=this.options_)&&o.autoBind&&(n.value=n.value.bind(null!=(l=e.proxy_)?l:e.target_));return f.make_(e,t,n,r)}function Se(e,t,n,r){var i,o,a;if(n.get)return Ue.extend_(e,t,n,r);if(n.set)return e.defineProperty_(t,{configurable:!Ot.safeDescriptors||e.isPlainObject_,set:He(t.toString(),n.set)},r);"function"==typeof n.value&&null!=(i=this.options_)&&i.autoBind&&(n.value=n.value.bind(null!=(a=e.proxy_)?a:e.target_));return(!1===(null==(o=this.options_)?void 0:o.deep)?Ne.ref:Ne).extend_(e,t,n,r)}function Ae(e,t){r("'"+this.annotationType_+"' cannot be used as a decorator")}var ke={deep:!0,name:void 0,defaultDecorator:void 0,proxy:!0};function je(e){return e||ke}Object.freeze(ke);var Ee=de("observable"),xe=de("observable.ref",{enhancer:Q}),Pe=de("observable.shallow",{enhancer:function(e,t,n){return null==e||Dr(e)||yr(e)||Sr(e)||Er(e)?e:Array.isArray(e)?Ne.array(e,{name:n,deep:!1}):m(e)?Ne.object(e,void 0,{name:n,deep:!1}):k(e)?Ne.map(e,{name:n,deep:!1}):j(e)?Ne.set(e,{name:n,deep:!1}):void 0}}),Re=de("observable.struct",{enhancer:function(e,t){return ti(e,t)?t:e}}),Ce=q(Ee);function Te(e){return!0===e.deep?J:!1===e.deep?Q:(t=e.defaultDecorator)&&null!=(n=null==(r=t.options_)?void 0:r.enhancer)?n:J;var t,n,r}function Ve(e,t,n){return H(t)?Ee.decorate_20223_(e,t):y(t)?void $(e,t,Ee):Cn(e)?e:m(e)?Ne.object(e,t,n):Array.isArray(e)?Ne.array(e,t):k(e)?Ne.map(e,t):j(e)?Ne.set(e,t):"object"==typeof e&&null!==e?e:Ne.box(e,t)}a(Ve,Ce);var Me,De,Ne=a(Ve,{box:function(e,t){var n=je(t);return new Qe(e,Te(n),n.name,!0,n.equals)},array:function(e,t){var n=je(t);return(!1===Ot.useProxies||!1===n.proxy?Fr:fr)(e,Te(n),n.name)},map:function(e,t){var n=je(t);return new wr(e,Te(n),n.name)},set:function(e,t){var n=je(t);return new jr(e,Te(n),n.name)},object:function(e,t,n){return Qr((function(){return fn(!1===Ot.useProxies||!1===(null==n?void 0:n.proxy)?Tr({},n):function(e,t){var n,r;return v(),e=Tr(e,t),null!=(r=(n=e[W]).proxy_)?r:n.proxy_=new Proxy(e,Jn)}({},n),e,t)}))},ref:q(xe),shallow:q(Pe),deep:Ce,struct:q(Re)}),Le="computed",Be=fe(Le),Ie=fe("computed.struct",{equals:Y.structural}),Ue=function(e,t){if(H(t))return Be.decorate_20223_(e,t);if(y(t))return $(e,t,Be);if(m(e))return q(fe(Le,e));var n=m(t)?t:{};return n.get=e,n.name||(n.name=e.name||""),new et(n)};Object.assign(Ue,Be),Ue.struct=q(Ie);var Ke=0,Ge=1,qe=null!=(Me=null==(De=s((function(){}),"name"))?void 0:De.configurable)&&Me,$e={value:"action",configurable:!0,writable:!1,enumerable:!1};function He(e,t,n,r){function i(){return We(e,n,t,r||this,arguments)}return void 0===n&&(n=!1),i.isMobxAction=!0,i.toString=function(){return t.toString()},qe&&($e.value=e,u(i,"name",$e)),i}function We(e,t,n,r,i){var o=ze(e,t,r,i);try{return n.apply(r,i)}catch(a){throw o.error_=a,a}finally{Fe(o)}}function ze(e,t,n,r){var i=Ot.trackingDerivation,o=!t||!i;Et();var a=Ot.allowStateChanges;o&&(ht(),a=Ye(!0));var s={runAsAction_:o,prevDerivation_:i,prevAllowStateChanges_:a,prevAllowStateReads_:vt(!0),notifySpy_:!1,startTime_:0,actionId_:Ge++,parentActionId_:Ke};return Ke=s.actionId_,s}function Fe(e){Ke!==e.actionId_&&r(30),Ke=e.parentActionId_,void 0!==e.error_&&(Ot.suppressReactionErrors=!0),Je(e.prevAllowStateChanges_),dt(e.prevAllowStateReads_),xt(),e.runAsAction_&&_t(e.prevDerivation_),Ot.suppressReactionErrors=!1}function Xe(e,t){var n=Ye(e);try{return t()}finally{Je(n)}}function Ye(e){var t=Ot.allowStateChanges;return Ot.allowStateChanges=e,t}function Je(e){Ot.allowStateChanges=e}var Qe=function(e){function t(t,n,r,i,o){var a;return void 0===r&&(r="ObservableValue"),void 0===i&&(i=!0),void 0===o&&(o=Y.default),(a=e.call(this,r)||this).enhancer=void 0,a.name_=void 0,a.equals=void 0,a.hasUnreportedChange_=!1,a.interceptors_=void 0,a.changeListeners_=void 0,a.value_=void 0,a.dehancer=void 0,a.enhancer=n,a.name_=r,a.equals=o,a.value_=n(t,void 0,r),a}I(t,e);var n=t.prototype;return n.dehanceValue=function(e){return void 0!==this.dehancer?this.dehancer(e):e},n.set=function(e){this.value_;if((e=this.prepareNewValue_(e))!==Ot.UNCHANGED){0,this.setNewValue_(e)}},n.prepareNewValue_=function(e){if(ut(this),Qn(this)){var t=er(this,{object:this,type:ur,newValue:e});if(!t)return Ot.UNCHANGED;e=t.newValue}return e=this.enhancer(e,this.value_,this.name_),this.equals(this.value_,e)?Ot.UNCHANGED:e},n.setNewValue_=function(e){var t=this.value_;this.value_=e,this.reportChanged(),tr(this)&&rr(this,{type:ur,object:this,newValue:e,oldValue:t})},n.get=function(){return this.reportObserved(),this.dehanceValue(this.value_)},n.intercept_=function(e){return Zn(this,e)},n.observe_=function(e,t){return t&&e({observableKind:"value",debugObjectName:this.name_,object:this,type:ur,newValue:this.value_,oldValue:void 0}),nr(this,e)},n.raw=function(){return this.value_},n.toJSON=function(){return this.get()},n.toString=function(){return this.name_+"["+this.value_+"]"},n.valueOf=function(){return P(this.get())},n[Symbol.toPrimitive]=function(){return this.valueOf()},t}(z),Ze=A("ObservableValue",Qe),et=function(){function e(e){this.dependenciesState_=tt.NOT_TRACKING_,this.observing_=[],this.newObserving_=null,this.observers_=new Set,this.runId_=0,this.lastAccessedBy_=0,this.lowestObserverState_=tt.UP_TO_DATE_,this.unboundDepsCount_=0,this.value_=new it(null),this.name_=void 0,this.triggeredBy_=void 0,this.flags_=0,this.derivation=void 0,this.setter_=void 0,this.isTracing_=nt.NONE,this.scope_=void 0,this.equals_=void 0,this.requiresReaction_=void 0,this.keepAlive_=void 0,this.onBOL=void 0,this.onBUOL=void 0,e.get||r(31),this.derivation=e.get,this.name_=e.name||"ComputedValue",e.set&&(this.setter_=He("ComputedValue-setter",e.set)),this.equals_=e.equals||(e.compareStructural||e.struct?Y.structural:Y.default),this.scope_=e.context,this.requiresReaction_=e.requiresReaction,this.keepAlive_=!!e.keepAlive}var t=e.prototype;return t.onBecomeStale_=function(){!function(e){if(e.lowestObserverState_!==tt.UP_TO_DATE_)return;e.lowestObserverState_=tt.POSSIBLY_STALE_,e.observers_.forEach((function(e){e.dependenciesState_===tt.UP_TO_DATE_&&(e.dependenciesState_=tt.POSSIBLY_STALE_,e.onBecomeStale_())}))}(this)},t.onBO=function(){this.onBOL&&this.onBOL.forEach((function(e){return e()}))},t.onBUO=function(){this.onBUOL&&this.onBUOL.forEach((function(e){return e()}))},t.get=function(){if(this.isComputing&&r(32,this.name_,this.derivation),0!==Ot.inBatch||0!==this.observers_.size||this.keepAlive_){if(Pt(this),at(this)){var e=Ot.trackingContext;this.keepAlive_&&!e&&(Ot.trackingContext=this),this.trackAndCompute()&&function(e){if(e.lowestObserverState_===tt.STALE_)return;e.lowestObserverState_=tt.STALE_,e.observers_.forEach((function(t){t.dependenciesState_===tt.POSSIBLY_STALE_?t.dependenciesState_=tt.STALE_:t.dependenciesState_===tt.UP_TO_DATE_&&(e.lowestObserverState_=tt.UP_TO_DATE_)}))}(this),Ot.trackingContext=e}}else at(this)&&(this.warnAboutUntrackedRead_(),Et(),this.value_=this.computeValue_(!1),xt());var t=this.value_;if(ot(t))throw t.cause;return t},t.set=function(e){if(this.setter_){this.isRunningSetter&&r(33,this.name_),this.isRunningSetter=!0;try{this.setter_.call(this.scope_,e)}finally{this.isRunningSetter=!1}}else r(34,this.name_)},t.trackAndCompute=function(){var e=this.value_,t=this.dependenciesState_===tt.NOT_TRACKING_,n=this.computeValue_(!0),r=t||ot(e)||ot(n)||!this.equals_(e,n);return r&&(this.value_=n),r},t.computeValue_=function(e){this.isComputing=!0;var t,n=Ye(!1);if(e)t=ct(this,this.derivation,this.scope_);else if(!0===Ot.disableErrorBoundaries)t=this.derivation.call(this.scope_);else try{t=this.derivation.call(this.scope_)}catch(r){t=new it(r)}return Je(n),this.isComputing=!1,t},t.suspend_=function(){this.keepAlive_||(lt(this),this.value_=void 0)},t.observe_=function(e,t){var n=this,r=!0,i=void 0;return Jt((function(){var o=n.get();if(!r||t){var a=ht();e({observableKind:"computed",debugObjectName:n.name_,type:ur,object:n,newValue:o,oldValue:i}),_t(a)}r=!1,i=o}))},t.warnAboutUntrackedRead_=function(){},t.toString=function(){return this.name_+"["+this.derivation.toString()+"]"},t.valueOf=function(){return P(this.get())},t[Symbol.toPrimitive]=function(){return this.valueOf()},N(e,[{key:"isComputing",get:function(){return T(this.flags_,e.isComputingMask_)},set:function(t){this.flags_=V(this.flags_,e.isComputingMask_,t)}},{key:"isRunningSetter",get:function(){return T(this.flags_,e.isRunningSetterMask_)},set:function(t){this.flags_=V(this.flags_,e.isRunningSetterMask_,t)}},{key:"isBeingObserved",get:function(){return T(this.flags_,e.isBeingObservedMask_)},set:function(t){this.flags_=V(this.flags_,e.isBeingObservedMask_,t)}},{key:"isPendingUnobservation",get:function(){return T(this.flags_,e.isPendingUnobservationMask_)},set:function(t){this.flags_=V(this.flags_,e.isPendingUnobservationMask_,t)}},{key:"diffValue",get:function(){return T(this.flags_,e.diffValueMask_)?1:0},set:function(t){this.flags_=V(this.flags_,e.diffValueMask_,1===t)}}])}();et.isComputingMask_=1,et.isRunningSetterMask_=2,et.isBeingObservedMask_=4,et.isPendingUnobservationMask_=8,et.diffValueMask_=16;var tt,nt,rt=A("ComputedValue",et);!function(e){e[e.NOT_TRACKING_=-1]="NOT_TRACKING_",e[e.UP_TO_DATE_=0]="UP_TO_DATE_",e[e.POSSIBLY_STALE_=1]="POSSIBLY_STALE_",e[e.STALE_=2]="STALE_"}(tt||(tt={})),function(e){e[e.NONE=0]="NONE",e[e.LOG=1]="LOG",e[e.BREAK=2]="BREAK"}(nt||(nt={}));var it=function(e){this.cause=void 0,this.cause=e};function ot(e){return e instanceof it}function at(e){switch(e.dependenciesState_){case tt.UP_TO_DATE_:return!1;case tt.NOT_TRACKING_:case tt.STALE_:return!0;case tt.POSSIBLY_STALE_:for(var t=vt(!0),n=ht(),r=e.observing_,i=r.length,o=0;o<i;o++){var a=r[o];if(rt(a)){if(Ot.disableErrorBoundaries)a.get();else try{a.get()}catch(s){return _t(n),dt(t),!0}if(e.dependenciesState_===tt.STALE_)return _t(n),dt(t),!0}}return pt(e),_t(n),dt(t),!1}}function st(){return null!==Ot.trackingDerivation}function ut(e){}function ct(e,t,n){var r=vt(!0);pt(e),e.newObserving_=new Array(0===e.runId_?100:e.observing_.length),e.unboundDepsCount_=0,e.runId_=++Ot.runId;var i,o=Ot.trackingDerivation;if(Ot.trackingDerivation=e,Ot.inBatch++,!0===Ot.disableErrorBoundaries)i=t.call(n);else try{i=t.call(n)}catch(a){i=new it(a)}return Ot.inBatch--,Ot.trackingDerivation=o,function(e){for(var t=e.observing_,n=e.observing_=e.newObserving_,r=tt.UP_TO_DATE_,i=0,o=e.unboundDepsCount_,a=0;a<o;a++){var s=n[a];0===s.diffValue&&(s.diffValue=1,i!==a&&(n[i]=s),i++),s.dependenciesState_>r&&(r=s.dependenciesState_)}n.length=i,e.newObserving_=null,o=t.length;for(;o--;){var u=t[o];0===u.diffValue&&kt(u,e),u.diffValue=0}for(;i--;){var c=n[i];1===c.diffValue&&(c.diffValue=0,At(c,e))}r!==tt.UP_TO_DATE_&&(e.dependenciesState_=r,e.onBecomeStale_())}(e),dt(r),i}function lt(e){var t=e.observing_;e.observing_=[];for(var n=t.length;n--;)kt(t[n],e);e.dependenciesState_=tt.NOT_TRACKING_}function ft(e){var t=ht();try{return e()}finally{_t(t)}}function ht(){var e=Ot.trackingDerivation;return Ot.trackingDerivation=null,e}function _t(e){Ot.trackingDerivation=e}function vt(e){var t=Ot.allowStateReads;return Ot.allowStateReads=e,t}function dt(e){Ot.allowStateReads=e}function pt(e){if(e.dependenciesState_!==tt.UP_TO_DATE_){e.dependenciesState_=tt.UP_TO_DATE_;for(var t=e.observing_,n=t.length;n--;)t[n].lowestObserverState_=tt.UP_TO_DATE_}}var bt=["mobxGuid","spyListeners","enforceActions","computedRequiresReaction","reactionRequiresObservable","observableRequiresReaction","allowStateReads","disableErrorBoundaries","runId","UNCHANGED","useProxies"],yt=function(){this.version=6,this.UNCHANGED={},this.trackingDerivation=null,this.trackingContext=null,this.runId=0,this.mobxGuid=0,this.inBatch=0,this.pendingUnobservations=[],this.pendingReactions=[],this.isRunningReactions=!1,this.allowStateChanges=!1,this.allowStateReads=!0,this.enforceActions=!0,this.spyListeners=[],this.globalReactionErrorHandlers=[],this.computedRequiresReaction=!1,this.reactionRequiresObservable=!1,this.observableRequiresReaction=!1,this.disableErrorBoundaries=!1,this.suppressReactionErrors=!1,this.useProxies=!0,this.verifyProxies=!1,this.safeDescriptors=!0},gt=!0,mt=!1,Ot=function(){var e=o();return e.__mobxInstanceCount>0&&!e.__mobxGlobals&&(gt=!1),e.__mobxGlobals&&e.__mobxGlobals.version!==(new yt).version&&(gt=!1),gt?e.__mobxGlobals?(e.__mobxInstanceCount+=1,e.__mobxGlobals.UNCHANGED||(e.__mobxGlobals.UNCHANGED={}),e.__mobxGlobals):(e.__mobxInstanceCount=1,e.__mobxGlobals=new yt):(setTimeout((function(){mt||r(35)}),1),new yt)}();function wt(){return Ot}function St(){var e=new yt;for(var t in e)-1===bt.indexOf(t)&&(Ot[t]=e[t]);Ot.allowStateChanges=!Ot.enforceActions}function At(e,t){e.observers_.add(t),e.lowestObserverState_>t.dependenciesState_&&(e.lowestObserverState_=t.dependenciesState_)}function kt(e,t){e.observers_.delete(t),0===e.observers_.size&&jt(e)}function jt(e){!1===e.isPendingUnobservation&&(e.isPendingUnobservation=!0,Ot.pendingUnobservations.push(e))}function Et(){Ot.inBatch++}function xt(){if(0==--Ot.inBatch){Dt();for(var e=Ot.pendingUnobservations,t=0;t<e.length;t++){var n=e[t];n.isPendingUnobservation=!1,0===n.observers_.size&&(n.isBeingObserved&&(n.isBeingObserved=!1,n.onBUO()),n instanceof et&&n.suspend_())}Ot.pendingUnobservations=[]}}function Pt(e){var t=Ot.trackingDerivation;return null!==t?(t.runId_!==e.lastAccessedBy_&&(e.lastAccessedBy_=t.runId_,t.newObserving_[t.unboundDepsCount_++]=e,!e.isBeingObserved&&Ot.trackingContext&&(e.isBeingObserved=!0,e.onBO())),e.isBeingObserved):(0===e.observers_.size&&Ot.inBatch>0&&jt(e),!1)}function Rt(e){e.lowestObserverState_!==tt.STALE_&&(e.lowestObserverState_=tt.STALE_,e.observers_.forEach((function(e){e.dependenciesState_===tt.UP_TO_DATE_&&e.onBecomeStale_(),e.dependenciesState_=tt.STALE_})))}var Ct=function(){function e(e,t,n,r){void 0===e&&(e="Reaction"),this.name_=void 0,this.onInvalidate_=void 0,this.errorHandler_=void 0,this.requiresObservable_=void 0,this.observing_=[],this.newObserving_=[],this.dependenciesState_=tt.NOT_TRACKING_,this.runId_=0,this.unboundDepsCount_=0,this.flags_=0,this.isTracing_=nt.NONE,this.name_=e,this.onInvalidate_=t,this.errorHandler_=n,this.requiresObservable_=r}var t=e.prototype;return t.onBecomeStale_=function(){this.schedule_()},t.schedule_=function(){this.isScheduled||(this.isScheduled=!0,Ot.pendingReactions.push(this),Dt())},t.runReaction_=function(){if(!this.isDisposed){Et(),this.isScheduled=!1;var e=Ot.trackingContext;if(Ot.trackingContext=this,at(this)){this.isTrackPending=!0;try{this.onInvalidate_()}catch(t){this.reportExceptionInDerivation_(t)}}Ot.trackingContext=e,xt()}},t.track=function(e){if(!this.isDisposed){Et();0,this.isRunning=!0;var t=Ot.trackingContext;Ot.trackingContext=this;var n=ct(this,e,void 0);Ot.trackingContext=t,this.isRunning=!1,this.isTrackPending=!1,this.isDisposed&&lt(this),ot(n)&&this.reportExceptionInDerivation_(n.cause),xt()}},t.reportExceptionInDerivation_=function(e){var t=this;if(this.errorHandler_)this.errorHandler_(e,this);else{if(Ot.disableErrorBoundaries)throw e;var n="[mobx] uncaught error in '"+this+"'";Ot.suppressReactionErrors||console.error(n,e),Ot.globalReactionErrorHandlers.forEach((function(n){return n(e,t)}))}},t.dispose=function(){this.isDisposed||(this.isDisposed=!0,this.isRunning||(Et(),lt(this),xt()))},t.getDisposer_=function(e){var t=this,n=function n(){t.dispose(),null==e||null==e.removeEventListener||e.removeEventListener("abort",n)};return null==e||null==e.addEventListener||e.addEventListener("abort",n),n[W]=this,n},t.toString=function(){return"Reaction["+this.name_+"]"},t.trace=function(e){void 0===e&&(e=!1),Wn(this,e)},N(e,[{key:"isDisposed",get:function(){return T(this.flags_,e.isDisposedMask_)},set:function(t){this.flags_=V(this.flags_,e.isDisposedMask_,t)}},{key:"isScheduled",get:function(){return T(this.flags_,e.isScheduledMask_)},set:function(t){this.flags_=V(this.flags_,e.isScheduledMask_,t)}},{key:"isTrackPending",get:function(){return T(this.flags_,e.isTrackPendingMask_)},set:function(t){this.flags_=V(this.flags_,e.isTrackPendingMask_,t)}},{key:"isRunning",get:function(){return T(this.flags_,e.isRunningMask_)},set:function(t){this.flags_=V(this.flags_,e.isRunningMask_,t)}},{key:"diffValue",get:function(){return T(this.flags_,e.diffValueMask_)?1:0},set:function(t){this.flags_=V(this.flags_,e.diffValueMask_,1===t)}}])}();function Tt(e){return Ot.globalReactionErrorHandlers.push(e),function(){var t=Ot.globalReactionErrorHandlers.indexOf(e);t>=0&&Ot.globalReactionErrorHandlers.splice(t,1)}}Ct.isDisposedMask_=1,Ct.isScheduledMask_=2,Ct.isTrackPendingMask_=4,Ct.isRunningMask_=8,Ct.diffValueMask_=16;var Vt=100,Mt=function(e){return e()};function Dt(){Ot.inBatch>0||Ot.isRunningReactions||Mt(Nt)}function Nt(){Ot.isRunningReactions=!0;for(var e=Ot.pendingReactions,t=0;e.length>0;){++t===Vt&&(console.error("[mobx] cycle in reaction: "+e[0]),e.splice(0));for(var n=e.splice(0),r=0,i=n.length;r<i;r++)n[r].runReaction_()}Ot.isRunningReactions=!1}var Lt=A("Reaction",Ct);function Bt(e){return console.warn("[mobx.spy] Is a no-op in production builds"),function(){}}var It="action",Ut="autoAction",Kt="<unnamed action>",Gt=te(It),qt=te("action.bound",{bound:!0}),$t=te(Ut,{autoAction:!0}),Ht=te("autoAction.bound",{autoAction:!0,bound:!0});function Wt(e){return function(t,n){return b(t)?He(t.name||Kt,t,e):b(n)?He(t,n,e):H(n)?(e?$t:Gt).decorate_20223_(t,n):y(n)?$(t,n,e?$t:Gt):y(t)?q(te(e?Ut:It,{name:t,autoAction:e})):void 0}}var zt=Wt(!1);Object.assign(zt,Gt);var Ft=Wt(!0);function Xt(e){return We(e.name||Kt,!1,e,this,void 0)}function Yt(e){return b(e)&&!0===e.isMobxAction}function Jt(e,t){var n,r,i,o;void 0===t&&(t=f);var a,s=null!=(n=null==(r=t)?void 0:r.name)?n:"Autorun";if(!t.scheduler&&!t.delay)a=new Ct(s,(function(){this.track(l)}),t.onError,t.requiresObservable);else{var u=Zt(t),c=!1;a=new Ct(s,(function(){c||(c=!0,u((function(){c=!1,a.isDisposed||a.track(l)})))}),t.onError,t.requiresObservable)}function l(){e(a)}return null!=(i=t)&&null!=(i=i.signal)&&i.aborted||a.schedule_(),a.getDisposer_(null==(o=t)?void 0:o.signal)}Object.assign(Ft,$t),zt.bound=q(qt),Ft.bound=q(Ht);var Qt=function(e){return e()};function Zt(e){return e.scheduler?e.scheduler:e.delay?function(t){return setTimeout(t,e.delay)}:Qt}function en(e,t,n){var r,i,o;void 0===n&&(n=f);var a,s,u,c=null!=(r=n.name)?r:"Reaction",l=zt(c,n.onError?(a=n.onError,s=t,function(){try{return s.apply(this,arguments)}catch(e){a.call(this,e)}}):t),h=!n.scheduler&&!n.delay,_=Zt(n),v=!0,d=!1,p=n.compareStructural?Y.structural:n.equals||Y.default,b=new Ct(c,(function(){v||h?y():d||(d=!0,_(y))}),n.onError,n.requiresObservable);function y(){if(d=!1,!b.isDisposed){var t=!1,r=u;b.track((function(){var n=Xe(!1,(function(){return e(b)}));t=v||!p(u,n),u=n})),(v&&n.fireImmediately||!v&&t)&&l(u,r,b),v=!1}}return null!=(i=n)&&null!=(i=i.signal)&&i.aborted||b.schedule_(),b.getDisposer_(null==(o=n)?void 0:o.signal)}var tn="onBO",nn="onBUO";function rn(e,t,n){return an(tn,e,t,n)}function on(e,t,n){return an(nn,e,t,n)}function an(e,t,n,r){var i="function"==typeof r?Xr(t,n):Xr(t),o=b(r)?r:n,a=e+"L";return i[a]?i[a].add(o):i[a]=new Set([o]),function(){var e=i[a];e&&(e.delete(o),0===e.size&&delete i[a])}}var sn="never",un="always",cn="observed";function ln(e){!0===e.isolateGlobalState&&function(){if((Ot.pendingReactions.length||Ot.inBatch||Ot.isRunningReactions)&&r(36),mt=!0,gt){var e=o();0==--e.__mobxInstanceCount&&(e.__mobxGlobals=void 0),Ot=new yt}}();var t,n,i=e.useProxies,a=e.enforceActions;if(void 0!==i&&(Ot.useProxies=i===un||i!==sn&&"undefined"!=typeof Proxy),"ifavailable"===i&&(Ot.verifyProxies=!0),void 0!==a){var s=a===un?un:a===cn;Ot.enforceActions=s,Ot.allowStateChanges=!0!==s&&s!==un}["computedRequiresReaction","reactionRequiresObservable","observableRequiresReaction","disableErrorBoundaries","safeDescriptors"].forEach((function(t){t in e&&(Ot[t]=!!e[t])})),Ot.allowStateReads=!Ot.observableRequiresReaction,e.reactionScheduler&&(t=e.reactionScheduler,n=Mt,Mt=function(e){return t((function(){return n(e)}))})}function fn(e,t,n,r){var i=C(t);return Qr((function(){var t=Tr(e,r)[W];x(i).forEach((function(e){t.extend_(e,i[e],!n||(!(e in n)||n[e]))}))})),e}function hn(e,t){return _n(Xr(e,t))}function _n(e){var t,n={name:e.name_};return e.observing_&&e.observing_.length>0&&(n.dependencies=(t=e.observing_,Array.from(new Set(t))).map(_n)),n}function vn(e,t){return dn(Xr(e,t))}function dn(e){var t={name:e.name_};return function(e){return e.observers_&&e.observers_.size>0}(e)&&(t.observers=Array.from(function(e){return e.observers_}(e)).map(dn)),t}var pn=0;function bn(){this.message="FLOW_CANCELLED"}function yn(e){return e instanceof bn}bn.prototype=Object.create(Error.prototype);var gn=ae("flow"),mn=ae("flow.bound",{bound:!0}),On=Object.assign((function(e,t){if(H(t))return gn.decorate_20223_(e,t);if(y(t))return $(e,t,gn);var n=e,r=n.name||"<unnamed flow>",i=function(){var e,t=arguments,i=++pn,o=zt(r+" - runid: "+i+" - init",n).apply(this,t),a=void 0,s=new Promise((function(t,n){var s=0;function u(e){var t;a=void 0;try{t=zt(r+" - runid: "+i+" - yield "+s++,o.next).call(o,e)}catch(u){return n(u)}l(t)}function c(e){var t;a=void 0;try{t=zt(r+" - runid: "+i+" - yield "+s++,o.throw).call(o,e)}catch(u){return n(u)}l(t)}function l(e){if(!b(null==e?void 0:e.then))return e.done?t(e.value):(a=Promise.resolve(e.value)).then(u,c);e.then(l,n)}e=n,u(void 0)}));return s.cancel=zt(r+" - runid: "+i+" - cancel",(function(){try{a&&wn(a);var t=o.return(void 0),n=Promise.resolve(t.value);n.then(p,p),wn(n),e(new bn)}catch(r){e(r)}})),s};return i.isMobXFlow=!0,i}),gn);function wn(e){b(e.cancel)&&e.cancel()}function Sn(e){return e}function An(e){return!0===(null==e?void 0:e.isMobXFlow)}function kn(e,t,n){var r;return Sr(e)||yr(e)||Ze(e)?r=Yr(e):Dr(e)&&(r=Yr(e,t)),r.dehancer="function"==typeof t?t:n,function(){r.dehancer=void 0}}function jn(e,t,n){return b(n)?function(e,t,n){return Yr(e,t).intercept_(n)}(e,t,n):function(e,t){return Yr(e).intercept_(t)}(e,t)}function En(e,t){if(void 0===t)return rt(e);if(!1===Dr(e))return!1;if(!e[W].values_.has(t))return!1;var n=Xr(e,t);return rt(n)}function xn(e){return En(e)}function Pn(e,t){return En(e,t)}function Rn(e,t){return!!e&&(void 0!==t?!!Dr(e)&&e[W].values_.has(t):Dr(e)||!!e[W]||F(e)||Lt(e)||rt(e))}function Cn(e){return Rn(e)}function Tn(e,t){return Rn(e,t)}function Vn(e){return Dr(e)?e[W].keys_():Sr(e)||Er(e)?Array.from(e.keys()):yr(e)?e.map((function(e,t){return t})):void r(5)}function Mn(e){return Dr(e)?Vn(e).map((function(t){return e[t]})):Sr(e)?Vn(e).map((function(t){return e.get(t)})):Er(e)?Array.from(e.values()):yr(e)?e.slice():void r(6)}function Dn(e){return Dr(e)?Vn(e).map((function(t){return[t,e[t]]})):Sr(e)?Vn(e).map((function(t){return[t,e.get(t)]})):Er(e)?Array.from(e.entries()):yr(e)?e.map((function(e,t){return[t,e]})):void r(7)}function Nn(e,t,n){if(2!==arguments.length||Er(e))Dr(e)?e[W].set_(t,n):Sr(e)?e.set(t,n):Er(e)?e.add(t):yr(e)?("number"!=typeof t&&(t=parseInt(t,10)),t<0&&r("Invalid index: '"+t+"'"),Et(),t>=e.length&&(e.length=t+1),e[t]=n,xt()):r(8);else{Et();var i=t;try{for(var o in i)Nn(e,o,i[o])}finally{xt()}}}function Ln(e,t){Dr(e)?e[W].delete_(t):Sr(e)||Er(e)?e.delete(t):yr(e)?("number"!=typeof t&&(t=parseInt(t,10)),e.splice(t,1)):r(9)}function Bn(e,t){return Dr(e)?e[W].has_(t):Sr(e)||Er(e)?e.has(t):yr(e)?t>=0&&t<e.length:void r(10)}function In(e,t){if(Bn(e,t))return Dr(e)?e[W].get_(t):Sr(e)?e.get(t):yr(e)?e[t]:void r(11)}function Un(e,t,n){if(Dr(e))return e[W].defineProperty_(t,n);r(39)}function Kn(e){if(Dr(e))return e[W].ownKeys_();r(38)}function Gn(e,t,n,r){return b(n)?function(e,t,n,r){return Yr(e,t).observe_(n,r)}(e,t,n,r):function(e,t,n){return Yr(e).observe_(t,n)}(e,t,n)}function qn(e,t,n){return e.set(t,n),n}function $n(e,t){if(null==e||"object"!=typeof e||e instanceof Date||!Cn(e))return e;if(Ze(e)||rt(e))return $n(e.get(),t);if(t.has(e))return t.get(e);if(yr(e)){var n=qn(t,e,new Array(e.length));return e.forEach((function(e,r){n[r]=$n(e,t)})),n}if(Er(e)){var r=qn(t,e,new Set);return e.forEach((function(e){r.add($n(e,t))})),r}if(Sr(e)){var i=qn(t,e,new Map);return e.forEach((function(e,n){i.set(n,$n(e,t))})),i}var o=qn(t,e,{});return Kn(e).forEach((function(n){c.propertyIsEnumerable.call(e,n)&&(o[n]=$n(e[n],t))})),o}function Hn(e,t){return $n(e,new Map)}function Wn(){}function zn(e,t){void 0===t&&(t=void 0),Et();try{return e.apply(t)}finally{xt()}}function Fn(e,t,n){return 1===arguments.length||t&&"object"==typeof t?function(e,t){var n,r,i;0;if(null!=t&&null!=(n=t.signal)&&n.aborted)return Object.assign(Promise.reject(new Error("WHEN_ABORTED")),{cancel:function(){return null}});var o=new Promise((function(n,o){var a,s=Xn(e,n,B({},t,{onError:o}));r=function(){s(),o(new Error("WHEN_CANCELLED"))},i=function(){s(),o(new Error("WHEN_ABORTED"))},null==t||null==(a=t.signal)||null==a.addEventListener||a.addEventListener("abort",i)})).finally((function(){var e;return null==t||null==(e=t.signal)||null==e.removeEventListener?void 0:e.removeEventListener("abort",i)}));return o.cancel=r,o}(e,t):Xn(e,t,n||{})}function Xn(e,t,n){var r;if("number"==typeof n.timeout){var i=new Error("WHEN_TIMEOUT");r=setTimeout((function(){if(!a[W].isDisposed){if(a(),!n.onError)throw i;n.onError(i)}}),n.timeout)}n.name="When";var o=He("When-effect",t),a=Jt((function(t){Xe(!1,e)&&(t.dispose(),r&&clearTimeout(r),o())}),n);return a}function Yn(e){return e[W]}On.bound=q(mn);var Jn={has:function(e,t){return Yn(e).has_(t)},get:function(e,t){return Yn(e).get_(t)},set:function(e,t,n){var r;return!!y(t)&&(null==(r=Yn(e).set_(t,n,!0))||r)},deleteProperty:function(e,t){var n;return!!y(t)&&(null==(n=Yn(e).delete_(t,!0))||n)},defineProperty:function(e,t,n){var r;return null==(r=Yn(e).defineProperty_(t,n))||r},ownKeys:function(e){return Yn(e).ownKeys_()},preventExtensions:function(e){r(13)}};function Qn(e){return void 0!==e.interceptors_&&e.interceptors_.length>0}function Zn(e,t){var n=e.interceptors_||(e.interceptors_=[]);return n.push(t),d((function(){var e=n.indexOf(t);-1!==e&&n.splice(e,1)}))}function er(e,t){var n=ht();try{for(var i=[].concat(e.interceptors_||[]),o=0,a=i.length;o<a&&((t=i[o](t))&&!t.type&&r(14),t);o++);return t}finally{_t(n)}}function tr(e){return void 0!==e.changeListeners_&&e.changeListeners_.length>0}function nr(e,t){var n=e.changeListeners_||(e.changeListeners_=[]);return n.push(t),d((function(){var e=n.indexOf(t);-1!==e&&n.splice(e,1)}))}function rr(e,t){var n=ht(),r=e.changeListeners_;if(r){for(var i=0,o=(r=r.slice()).length;i<o;i++)r[i](t);_t(n)}}function ir(e,t,n){return Qr((function(){var r=Tr(e,n)[W];null!=t||(t=function(e){return R(e,G)||w(e,G,B({},e[G])),e[G]}(e)),x(t).forEach((function(e){return r.make_(e,t[e])}))})),e}var or=Symbol("mobx-keys");function ar(e,t,n){return m(e)?fn(e,e,t,n):(Qr((function(){var r=Tr(e,n)[W];if(!e[or]){var i=Object.getPrototypeOf(e),o=new Set([].concat(x(e),x(i)));o.delete("constructor"),o.delete(W),w(i,or,o)}e[or].forEach((function(e){return r.make_(e,!t||(!(e in t)||t[e]))}))})),e)}var sr="splice",ur="update",cr={get:function(e,t){var n=e[W];return t===W?n:"length"===t?n.getArrayLength_():"string"!=typeof t||isNaN(t)?R(hr,t)?hr[t]:e[t]:n.get_(parseInt(t))},set:function(e,t,n){var r=e[W];return"length"===t&&r.setArrayLength_(n),"symbol"==typeof t||isNaN(t)?e[t]=n:r.set_(parseInt(t),n),!0},preventExtensions:function(){r(15)}},lr=function(){function e(e,t,n,r){void 0===e&&(e="ObservableArray"),this.owned_=void 0,this.legacyMode_=void 0,this.atom_=void 0,this.values_=[],this.interceptors_=void 0,this.changeListeners_=void 0,this.enhancer_=void 0,this.dehancer=void 0,this.proxy_=void 0,this.lastKnownLength_=0,this.owned_=n,this.legacyMode_=r,this.atom_=new z(e),this.enhancer_=function(e,n){return t(e,n,"ObservableArray[..]")}}var t=e.prototype;return t.dehanceValue_=function(e){return void 0!==this.dehancer?this.dehancer(e):e},t.dehanceValues_=function(e){return void 0!==this.dehancer&&e.length>0?e.map(this.dehancer):e},t.intercept_=function(e){return Zn(this,e)},t.observe_=function(e,t){return void 0===t&&(t=!1),t&&e({observableKind:"array",object:this.proxy_,debugObjectName:this.atom_.name_,type:"splice",index:0,added:this.values_.slice(),addedCount:this.values_.length,removed:[],removedCount:0}),nr(this,e)},t.getArrayLength_=function(){return this.atom_.reportObserved(),this.values_.length},t.setArrayLength_=function(e){("number"!=typeof e||isNaN(e)||e<0)&&r("Out of range: "+e);var t=this.values_.length;if(e!==t)if(e>t){for(var n=new Array(e-t),i=0;i<e-t;i++)n[i]=void 0;this.spliceWithArray_(t,0,n)}else this.spliceWithArray_(e,t-e)},t.updateArrayLength_=function(e,t){e!==this.lastKnownLength_&&r(16),this.lastKnownLength_+=t,this.legacyMode_&&t>0&&zr(e+t+1)},t.spliceWithArray_=function(e,t,n){var r=this;this.atom_;var i=this.values_.length;if(void 0===e?e=0:e>i?e=i:e<0&&(e=Math.max(0,i+e)),t=1===arguments.length?i-e:null==t?0:Math.max(0,Math.min(t,i-e)),void 0===n&&(n=l),Qn(this)){var o=er(this,{object:this.proxy_,type:sr,index:e,removedCount:t,added:n});if(!o)return l;t=o.removedCount,n=o.added}if(n=0===n.length?n:n.map((function(e){return r.enhancer_(e,void 0)})),this.legacyMode_){var a=n.length-t;this.updateArrayLength_(i,a)}var s=this.spliceItemsIntoValues_(e,t,n);return 0===t&&0===n.length||this.notifyArraySplice_(e,n,s),this.dehanceValues_(s)},t.spliceItemsIntoValues_=function(e,t,n){var r;if(n.length<1e4)return(r=this.values_).splice.apply(r,[e,t].concat(n));var i=this.values_.slice(e,e+t),o=this.values_.slice(e+t);this.values_.length+=n.length-t;for(var a=0;a<n.length;a++)this.values_[e+a]=n[a];for(var s=0;s<o.length;s++)this.values_[e+n.length+s]=o[s];return i},t.notifyArrayChildUpdate_=function(e,t,n){var r=!this.owned_&&!1,i=tr(this),o=i||r?{observableKind:"array",object:this.proxy_,type:ur,debugObjectName:this.atom_.name_,index:e,newValue:t,oldValue:n}:null;this.atom_.reportChanged(),i&&rr(this,o)},t.notifyArraySplice_=function(e,t,n){var r=!this.owned_&&!1,i=tr(this),o=i||r?{observableKind:"array",object:this.proxy_,debugObjectName:this.atom_.name_,type:sr,index:e,removed:n,added:t,removedCount:n.length,addedCount:t.length}:null;this.atom_.reportChanged(),i&&rr(this,o)},t.get_=function(e){if(!(this.legacyMode_&&e>=this.values_.length))return this.atom_.reportObserved(),this.dehanceValue_(this.values_[e]);console.warn("[mobx] Out of bounds read: "+e)},t.set_=function(e,t){var n=this.values_;if(this.legacyMode_&&e>n.length&&r(17,e,n.length),e<n.length){this.atom_;var i=n[e];if(Qn(this)){var o=er(this,{type:ur,object:this.proxy_,index:e,newValue:t});if(!o)return;t=o.newValue}(t=this.enhancer_(t,i))!==i&&(n[e]=t,this.notifyArrayChildUpdate_(e,t,i))}else{for(var a=new Array(e+1-n.length),s=0;s<a.length-1;s++)a[s]=void 0;a[a.length-1]=t,this.spliceWithArray_(n.length,0,a)}},e}();function fr(e,t,n,r){return void 0===n&&(n="ObservableArray"),void 0===r&&(r=!1),v(),Qr((function(){var i=new lr(n,t,r,!1);S(i.values_,W,i);var o=new Proxy(i.values_,cr);return i.proxy_=o,e&&e.length&&i.spliceWithArray_(0,0,e),o}))}var hr={clear:function(){return this.splice(0)},replace:function(e){var t=this[W];return t.spliceWithArray_(0,t.values_.length,e)},toJSON:function(){return this.slice()},splice:function(e,t){for(var n=arguments.length,r=new Array(n>2?n-2:0),i=2;i<n;i++)r[i-2]=arguments[i];var o=this[W];switch(arguments.length){case 0:return[];case 1:return o.spliceWithArray_(e);case 2:return o.spliceWithArray_(e,t)}return o.spliceWithArray_(e,t,r)},spliceWithArray:function(e,t,n){return this[W].spliceWithArray_(e,t,n)},push:function(){for(var e=this[W],t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.spliceWithArray_(e.values_.length,0,n),e.values_.length},pop:function(){return this.splice(Math.max(this[W].values_.length-1,0),1)[0]},shift:function(){return this.splice(0,1)[0]},unshift:function(){for(var e=this[W],t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.spliceWithArray_(0,0,n),e.values_.length},reverse:function(){return Ot.trackingDerivation&&r(37,"reverse"),this.replace(this.slice().reverse()),this},sort:function(){Ot.trackingDerivation&&r(37,"sort");var e=this.slice();return e.sort.apply(e,arguments),this.replace(e),this},remove:function(e){var t=this[W],n=t.dehanceValues_(t.values_).indexOf(e);return n>-1&&(this.splice(n,1),!0)}};function _r(e,t){"function"==typeof Array.prototype[e]&&(hr[e]=t(e))}function vr(e){return function(){var t=this[W];t.atom_.reportObserved();var n=t.dehanceValues_(t.values_);return n[e].apply(n,arguments)}}function dr(e){return function(t,n){var r=this,i=this[W];return i.atom_.reportObserved(),i.dehanceValues_(i.values_)[e]((function(e,i){return t.call(n,e,i,r)}))}}function pr(e){return function(){var t=this,n=this[W];n.atom_.reportObserved();var r=n.dehanceValues_(n.values_),i=arguments[0];return arguments[0]=function(e,n,r){return i(e,n,r,t)},r[e].apply(r,arguments)}}_r("at",vr),_r("concat",vr),_r("flat",vr),_r("includes",vr),_r("indexOf",vr),_r("join",vr),_r("lastIndexOf",vr),_r("slice",vr),_r("toString",vr),_r("toLocaleString",vr),_r("toSorted",vr),_r("toSpliced",vr),_r("with",vr),_r("every",dr),_r("filter",dr),_r("find",dr),_r("findIndex",dr),_r("findLast",dr),_r("findLastIndex",dr),_r("flatMap",dr),_r("forEach",dr),_r("map",dr),_r("some",dr),_r("toReversed",dr),_r("reduce",pr),_r("reduceRight",pr);var br=A("ObservableArrayAdministration",lr);function yr(e){return g(e)&&br(e[W])}var gr={},mr="add",Or="delete",wr=function(){function e(e,t,n){var i=this;void 0===t&&(t=J),void 0===n&&(n="ObservableMap"),this.enhancer_=void 0,this.name_=void 0,this[W]=gr,this.data_=void 0,this.hasMap_=void 0,this.keysAtom_=void 0,this.interceptors_=void 0,this.changeListeners_=void 0,this.dehancer=void 0,this.enhancer_=t,this.name_=n,b(Map)||r(18),Qr((function(){i.keysAtom_=X("ObservableMap.keys()"),i.data_=new Map,i.hasMap_=new Map,e&&i.merge(e)}))}var t=e.prototype;return t.has_=function(e){return this.data_.has(e)},t.has=function(e){var t=this;if(!Ot.trackingDerivation)return this.has_(e);var n=this.hasMap_.get(e);if(!n){var r=n=new Qe(this.has_(e),Q,"ObservableMap.key?",!1);this.hasMap_.set(e,r),on(r,(function(){return t.hasMap_.delete(e)}))}return n.get()},t.set=function(e,t){var n=this.has_(e);if(Qn(this)){var r=er(this,{type:n?ur:mr,object:this,newValue:t,name:e});if(!r)return this;t=r.newValue}return n?this.updateValue_(e,t):this.addValue_(e,t),this},t.delete=function(e){var t=this;if((this.keysAtom_,Qn(this))&&!er(this,{type:Or,object:this,name:e}))return!1;if(this.has_(e)){var n=tr(this),r=n?{observableKind:"map",debugObjectName:this.name_,type:Or,object:this,oldValue:this.data_.get(e).value_,name:e}:null;return zn((function(){var n;t.keysAtom_.reportChanged(),null==(n=t.hasMap_.get(e))||n.setNewValue_(!1),t.data_.get(e).setNewValue_(void 0),t.data_.delete(e)})),n&&rr(this,r),!0}return!1},t.updateValue_=function(e,t){var n=this.data_.get(e);if((t=n.prepareNewValue_(t))!==Ot.UNCHANGED){var r=tr(this),i=r?{observableKind:"map",debugObjectName:this.name_,type:ur,object:this,oldValue:n.value_,name:e,newValue:t}:null;0,n.setNewValue_(t),r&&rr(this,i)}},t.addValue_=function(e,t){var n=this;this.keysAtom_,zn((function(){var r,i=new Qe(t,n.enhancer_,"ObservableMap.key",!1);n.data_.set(e,i),t=i.value_,null==(r=n.hasMap_.get(e))||r.setNewValue_(!0),n.keysAtom_.reportChanged()}));var r=tr(this),i=r?{observableKind:"map",debugObjectName:this.name_,type:mr,object:this,name:e,newValue:t}:null;r&&rr(this,i)},t.get=function(e){return this.has(e)?this.dehanceValue_(this.data_.get(e).get()):this.dehanceValue_(void 0)},t.dehanceValue_=function(e){return void 0!==this.dehancer?this.dehancer(e):e},t.keys=function(){return this.keysAtom_.reportObserved(),this.data_.keys()},t.values=function(){var e=this,t=this.keys();return Ar({next:function(){var n=t.next(),r=n.done,i=n.value;return{done:r,value:r?void 0:e.get(i)}}})},t.entries=function(){var e=this,t=this.keys();return Ar({next:function(){var n=t.next(),r=n.done,i=n.value;return{done:r,value:r?void 0:[i,e.get(i)]}}})},t[Symbol.iterator]=function(){return this.entries()},t.forEach=function(e,t){for(var n,r=L(this);!(n=r()).done;){var i=n.value,o=i[0],a=i[1];e.call(t,a,o,this)}},t.merge=function(e){var t=this;return Sr(e)&&(e=new Map(e)),zn((function(){var n,i,o;m(e)?function(e){var t=Object.keys(e);if(!E)return t;var n=Object.getOwnPropertySymbols(e);return n.length?[].concat(t,n.filter((function(t){return c.propertyIsEnumerable.call(e,t)}))):t}(e).forEach((function(n){return t.set(n,e[n])})):Array.isArray(e)?e.forEach((function(e){var n=e[0],r=e[1];return t.set(n,r)})):k(e)?(n=e,i=Object.getPrototypeOf(n),o=Object.getPrototypeOf(i),null!==Object.getPrototypeOf(o)&&r(19,e),e.forEach((function(e,n){return t.set(n,e)}))):null!=e&&r(20,e)})),this},t.clear=function(){var e=this;zn((function(){ft((function(){for(var t,n=L(e.keys());!(t=n()).done;){var r=t.value;e.delete(r)}}))}))},t.replace=function(e){var t=this;return zn((function(){for(var n,i=function(e){if(k(e)||Sr(e))return e;if(Array.isArray(e))return new Map(e);if(m(e)){var t=new Map;for(var n in e)t.set(n,e[n]);return t}return r(21,e)}(e),o=new Map,a=!1,s=L(t.data_.keys());!(n=s()).done;){var u=n.value;if(!i.has(u))if(t.delete(u))a=!0;else{var c=t.data_.get(u);o.set(u,c)}}for(var l,f=L(i.entries());!(l=f()).done;){var h=l.value,_=h[0],v=h[1],d=t.data_.has(_);if(t.set(_,v),t.data_.has(_)){var p=t.data_.get(_);o.set(_,p),d||(a=!0)}}if(!a)if(t.data_.size!==o.size)t.keysAtom_.reportChanged();else for(var b=t.data_.keys(),y=o.keys(),g=b.next(),O=y.next();!g.done;){if(g.value!==O.value){t.keysAtom_.reportChanged();break}g=b.next(),O=y.next()}t.data_=o})),this},t.toString=function(){return"[object ObservableMap]"},t.toJSON=function(){return Array.from(this)},t.observe_=function(e,t){return nr(this,e)},t.intercept_=function(e){return Zn(this,e)},N(e,[{key:"size",get:function(){return this.keysAtom_.reportObserved(),this.data_.size}},{key:Symbol.toStringTag,get:function(){return"Map"}}])}(),Sr=A("ObservableMap",wr);function Ar(e){return e[Symbol.toStringTag]="MapIterator",oi(e)}var kr={},jr=function(){function e(e,t,n){var i=this;void 0===t&&(t=J),void 0===n&&(n="ObservableSet"),this.name_=void 0,this[W]=kr,this.data_=new Set,this.atom_=void 0,this.changeListeners_=void 0,this.interceptors_=void 0,this.dehancer=void 0,this.enhancer_=void 0,this.name_=n,b(Set)||r(22),this.enhancer_=function(e,r){return t(e,r,n)},Qr((function(){i.atom_=X(i.name_),e&&i.replace(e)}))}var t=e.prototype;return t.dehanceValue_=function(e){return void 0!==this.dehancer?this.dehancer(e):e},t.clear=function(){var e=this;zn((function(){ft((function(){for(var t,n=L(e.data_.values());!(t=n()).done;){var r=t.value;e.delete(r)}}))}))},t.forEach=function(e,t){for(var n,r=L(this);!(n=r()).done;){var i=n.value;e.call(t,i,i,this)}},t.add=function(e){var t=this;if((this.atom_,Qn(this))&&!er(this,{type:mr,object:this,newValue:e}))return this;if(!this.has(e)){zn((function(){t.data_.add(t.enhancer_(e,void 0)),t.atom_.reportChanged()}));var n=!1,r=tr(this),i=r?{observableKind:"set",debugObjectName:this.name_,type:mr,object:this,newValue:e}:null;n,r&&rr(this,i)}return this},t.delete=function(e){var t=this;if(Qn(this)&&!er(this,{type:Or,object:this,oldValue:e}))return!1;if(this.has(e)){var n=tr(this),r=n?{observableKind:"set",debugObjectName:this.name_,type:Or,object:this,oldValue:e}:null;return zn((function(){t.atom_.reportChanged(),t.data_.delete(e)})),n&&rr(this,r),!0}return!1},t.has=function(e){return this.atom_.reportObserved(),this.data_.has(this.dehanceValue_(e))},t.entries=function(){var e=0,t=Array.from(this.keys()),n=Array.from(this.values());return xr({next:function(){var r=e;return e+=1,r<n.length?{value:[t[r],n[r]],done:!1}:{value:void 0,done:!0}}})},t.keys=function(){return this.values()},t.values=function(){this.atom_.reportObserved();var e=this,t=0,n=Array.from(this.data_.values());return xr({next:function(){return t<n.length?{value:e.dehanceValue_(n[t++]),done:!1}:{value:void 0,done:!0}}})},t.intersection=function(e){return j(e)&&!Er(e)?e.intersection(this):new Set(this).intersection(e)},t.union=function(e){return j(e)&&!Er(e)?e.union(this):new Set(this).union(e)},t.difference=function(e){return new Set(this).difference(e)},t.symmetricDifference=function(e){return j(e)&&!Er(e)?e.symmetricDifference(this):new Set(this).symmetricDifference(e)},t.isSubsetOf=function(e){return new Set(this).isSubsetOf(e)},t.isSupersetOf=function(e){return new Set(this).isSupersetOf(e)},t.isDisjointFrom=function(e){return j(e)&&!Er(e)?e.isDisjointFrom(this):new Set(this).isDisjointFrom(e)},t.replace=function(e){var t=this;return Er(e)&&(e=new Set(e)),zn((function(){Array.isArray(e)||j(e)?(t.clear(),e.forEach((function(e){return t.add(e)}))):null!=e&&r("Cannot initialize set from "+e)})),this},t.observe_=function(e,t){return nr(this,e)},t.intercept_=function(e){return Zn(this,e)},t.toJSON=function(){return Array.from(this)},t.toString=function(){return"[object ObservableSet]"},t[Symbol.iterator]=function(){return this.values()},N(e,[{key:"size",get:function(){return this.atom_.reportObserved(),this.data_.size}},{key:Symbol.toStringTag,get:function(){return"Set"}}])}(),Er=A("ObservableSet",jr);function xr(e){return e[Symbol.toStringTag]="SetIterator",oi(e)}var Pr=Object.create(null),Rr="remove",Cr=function(){function e(e,t,n,r){void 0===t&&(t=new Map),void 0===r&&(r=me),this.target_=void 0,this.values_=void 0,this.name_=void 0,this.defaultAnnotation_=void 0,this.keysAtom_=void 0,this.changeListeners_=void 0,this.interceptors_=void 0,this.proxy_=void 0,this.isPlainObject_=void 0,this.appliedAnnotations_=void 0,this.pendingKeys_=void 0,this.target_=e,this.values_=t,this.name_=n,this.defaultAnnotation_=r,this.keysAtom_=new z("ObservableObject.keys"),this.isPlainObject_=m(this.target_)}var t=e.prototype;return t.getObservablePropValue_=function(e){return this.values_.get(e).get()},t.setObservablePropValue_=function(e,t){var n=this.values_.get(e);if(n instanceof et)return n.set(t),!0;if(Qn(this)){var r=er(this,{type:ur,object:this.proxy_||this.target_,name:e,newValue:t});if(!r)return null;t=r.newValue}if((t=n.prepareNewValue_(t))!==Ot.UNCHANGED){var i=tr(this),o=i?{type:ur,observableKind:"object",debugObjectName:this.name_,object:this.proxy_||this.target_,oldValue:n.value_,name:e,newValue:t}:null;0,n.setNewValue_(t),i&&rr(this,o)}return!0},t.get_=function(e){return Ot.trackingDerivation&&!R(this.target_,e)&&this.has_(e),this.target_[e]},t.set_=function(e,t,n){return void 0===n&&(n=!1),R(this.target_,e)?this.values_.has(e)?this.setObservablePropValue_(e,t):n?Reflect.set(this.target_,e,t):(this.target_[e]=t,!0):this.extend_(e,{value:t,enumerable:!0,writable:!0,configurable:!0},this.defaultAnnotation_,n)},t.has_=function(e){if(!Ot.trackingDerivation)return e in this.target_;this.pendingKeys_||(this.pendingKeys_=new Map);var t=this.pendingKeys_.get(e);return t||(t=new Qe(e in this.target_,Q,"ObservableObject.key?",!1),this.pendingKeys_.set(e,t)),t.get()},t.make_=function(e,t){if(!0===t&&(t=this.defaultAnnotation_),!1!==t){if(Lr(this,t,e),!(e in this.target_)){var n;if(null!=(n=this.target_[G])&&n[e])return;r(1,t.annotationType_,this.name_+"."+e.toString())}for(var i=this.target_;i&&i!==c;){var o=s(i,e);if(o){var a=t.make_(this,e,o,i);if(0===a)return;if(1===a)break}i=Object.getPrototypeOf(i)}Nr(this,t,e)}},t.extend_=function(e,t,n,r){if(void 0===r&&(r=!1),!0===n&&(n=this.defaultAnnotation_),!1===n)return this.defineProperty_(e,t,r);Lr(this,n,e);var i=n.extend_(this,e,t,r);return i&&Nr(this,n,e),i},t.defineProperty_=function(e,t,n){void 0===n&&(n=!1),this.keysAtom_;try{Et();var r=this.delete_(e);if(!r)return r;if(Qn(this)){var i=er(this,{object:this.proxy_||this.target_,name:e,type:mr,newValue:t.value});if(!i)return null;var o=i.newValue;t.value!==o&&(t=B({},t,{value:o}))}if(n){if(!Reflect.defineProperty(this.target_,e,t))return!1}else u(this.target_,e,t);this.notifyPropertyAddition_(e,t.value)}finally{xt()}return!0},t.defineObservableProperty_=function(e,t,n,r){void 0===r&&(r=!1),this.keysAtom_;try{Et();var i=this.delete_(e);if(!i)return i;if(Qn(this)){var o=er(this,{object:this.proxy_||this.target_,name:e,type:mr,newValue:t});if(!o)return null;t=o.newValue}var a=Mr(e),s={configurable:!Ot.safeDescriptors||this.isPlainObject_,enumerable:!0,get:a.get,set:a.set};if(r){if(!Reflect.defineProperty(this.target_,e,s))return!1}else u(this.target_,e,s);var c=new Qe(t,n,"ObservableObject.key",!1);this.values_.set(e,c),this.notifyPropertyAddition_(e,c.value_)}finally{xt()}return!0},t.defineComputedProperty_=function(e,t,n){void 0===n&&(n=!1),this.keysAtom_;try{Et();var r=this.delete_(e);if(!r)return r;if(Qn(this))if(!er(this,{object:this.proxy_||this.target_,name:e,type:mr,newValue:void 0}))return null;t.name||(t.name="ObservableObject.key"),t.context=this.proxy_||this.target_;var i=Mr(e),o={configurable:!Ot.safeDescriptors||this.isPlainObject_,enumerable:!1,get:i.get,set:i.set};if(n){if(!Reflect.defineProperty(this.target_,e,o))return!1}else u(this.target_,e,o);this.values_.set(e,new et(t)),this.notifyPropertyAddition_(e,void 0)}finally{xt()}return!0},t.delete_=function(e,t){if(void 0===t&&(t=!1),this.keysAtom_,!R(this.target_,e))return!0;if(Qn(this)&&!er(this,{object:this.proxy_||this.target_,name:e,type:Rr}))return null;try{var n;Et();var r,i=tr(this),o=this.values_.get(e),a=void 0;if(!o&&i)a=null==(r=s(this.target_,e))?void 0:r.value;if(t){if(!Reflect.deleteProperty(this.target_,e))return!1}else delete this.target_[e];if(o&&(this.values_.delete(e),o instanceof Qe&&(a=o.value_),Rt(o)),this.keysAtom_.reportChanged(),null==(n=this.pendingKeys_)||null==(n=n.get(e))||n.set(e in this.target_),i){var u={type:Rr,observableKind:"object",object:this.proxy_||this.target_,debugObjectName:this.name_,oldValue:a,name:e};0,i&&rr(this,u)}}finally{xt()}return!0},t.observe_=function(e,t){return nr(this,e)},t.intercept_=function(e){return Zn(this,e)},t.notifyPropertyAddition_=function(e,t){var n,r=tr(this);if(r){var i=r?{type:mr,observableKind:"object",debugObjectName:this.name_,object:this.proxy_||this.target_,name:e,newValue:t}:null;0,r&&rr(this,i)}null==(n=this.pendingKeys_)||null==(n=n.get(e))||n.set(!0),this.keysAtom_.reportChanged()},t.ownKeys_=function(){return this.keysAtom_.reportObserved(),x(this.target_)},t.keys_=function(){return this.keysAtom_.reportObserved(),Object.keys(this.target_)},e}();function Tr(e,t){var n;if(R(e,W))return e;var r=null!=(n=null==t?void 0:t.name)?n:"ObservableObject",i=new Cr(e,new Map,String(r),function(e){var t;return e?null!=(t=e.defaultDecorator)?t:Oe(e):void 0}(t));return w(e,W,i),e}var Vr=A("ObservableObjectAdministration",Cr);function Mr(e){return Pr[e]||(Pr[e]={get:function(){return this[W].getObservablePropValue_(e)},set:function(t){return this[W].setObservablePropValue_(e,t)}})}function Dr(e){return!!g(e)&&Vr(e[W])}function Nr(e,t,n){var r;null==(r=e.target_[G])||delete r[n]}function Lr(e,t,n){}var Br,Ir,Ur=Hr(0),Kr=function(){var e=!1,t={};return Object.defineProperty(t,"0",{set:function(){e=!0}}),Object.create(t)[0]=1,!1===e}(),Gr=0,qr=function(){};Br=qr,Ir=Array.prototype,Object.setPrototypeOf?Object.setPrototypeOf(Br.prototype,Ir):void 0!==Br.prototype.__proto__?Br.prototype.__proto__=Ir:Br.prototype=Ir;var $r=function(e){function t(t,n,r,i){var o;return void 0===r&&(r="ObservableArray"),void 0===i&&(i=!1),o=e.call(this)||this,Qr((function(){var e=new lr(r,n,i,!0);e.proxy_=o,S(o,W,e),t&&t.length&&o.spliceWithArray(0,0,t),Kr&&Object.defineProperty(o,"0",Ur)})),o}I(t,e);var n=t.prototype;return n.concat=function(){this[W].atom_.reportObserved();for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Array.prototype.concat.apply(this.slice(),t.map((function(e){return yr(e)?e.slice():e})))},n[Symbol.iterator]=function(){var e=this,t=0;return oi({next:function(){return t<e.length?{value:e[t++],done:!1}:{done:!0,value:void 0}}})},N(t,[{key:"length",get:function(){return this[W].getArrayLength_()},set:function(e){this[W].setArrayLength_(e)}},{key:Symbol.toStringTag,get:function(){return"Array"}}])}(qr);function Hr(e){return{enumerable:!1,configurable:!0,get:function(){return this[W].get_(e)},set:function(t){this[W].set_(e,t)}}}function Wr(e){u($r.prototype,""+e,Hr(e))}function zr(e){if(e>Gr){for(var t=Gr;t<e+100;t++)Wr(t);Gr=e}}function Fr(e,t,n){return new $r(e,t,n)}function Xr(e,t){if("object"==typeof e&&null!==e){if(yr(e))return void 0!==t&&r(23),e[W].atom_;if(Er(e))return e.atom_;if(Sr(e)){if(void 0===t)return e.keysAtom_;var n=e.data_.get(t)||e.hasMap_.get(t);return n||r(25,t,Jr(e)),n}if(Dr(e)){if(!t)return r(26);var i=e[W].values_.get(t);return i||r(27,t,Jr(e)),i}if(F(e)||rt(e)||Lt(e))return e}else if(b(e)&&Lt(e[W]))return e[W];r(28)}function Yr(e,t){return e||r(29),void 0!==t?Yr(Xr(e,t)):F(e)||rt(e)||Lt(e)||Sr(e)||Er(e)?e:e[W]?e[W]:void r(24,e)}function Jr(e,t){var n;if(void 0!==t)n=Xr(e,t);else{if(Yt(e))return e.name;n=Dr(e)||Sr(e)||Er(e)?Yr(e):Xr(e)}return n.name_}function Qr(e){var t=ht(),n=Ye(!0);Et();try{return e()}finally{xt(),Je(n),_t(t)}}Object.entries(hr).forEach((function(e){var t=e[0],n=e[1];"concat"!==t&&w($r.prototype,t,n)})),zr(1e3);var Zr,ei=c.toString;function ti(e,t,n){return void 0===n&&(n=-1),ni(e,t,n)}function ni(e,t,n,r,i){if(e===t)return 0!==e||1/e==1/t;if(null==e||null==t)return!1;if(e!=e)return t!=t;var o=typeof e;if("function"!==o&&"object"!==o&&"object"!=typeof t)return!1;var a=ei.call(e);if(a!==ei.call(t))return!1;switch(a){case"[object RegExp]":case"[object String]":return""+e==""+t;case"[object Number]":return+e!=+e?+t!=+t:0==+e?1/+e==1/t:+e==+t;case"[object Date]":case"[object Boolean]":return+e==+t;case"[object Symbol]":return"undefined"!=typeof Symbol&&Symbol.valueOf.call(e)===Symbol.valueOf.call(t);case"[object Map]":case"[object Set]":n>=0&&n++}e=ri(e),t=ri(t);var s="[object Array]"===a;if(!s){if("object"!=typeof e||"object"!=typeof t)return!1;var u=e.constructor,c=t.constructor;if(u!==c&&!(b(u)&&u instanceof u&&b(c)&&c instanceof c)&&"constructor"in e&&"constructor"in t)return!1}if(0===n)return!1;n<0&&(n=-1),i=i||[];for(var l=(r=r||[]).length;l--;)if(r[l]===e)return i[l]===t;if(r.push(e),i.push(t),s){if((l=e.length)!==t.length)return!1;for(;l--;)if(!ni(e[l],t[l],n-1,r,i))return!1}else{var f,h=Object.keys(e);if(l=h.length,Object.keys(t).length!==l)return!1;for(;l--;)if(!R(t,f=h[l])||!ni(e[f],t[f],n-1,r,i))return!1}return r.pop(),i.pop(),!0}function ri(e){return yr(e)?e.slice():k(e)||Sr(e)||j(e)||Er(e)?Array.from(e.entries()):e}var ii=(null==(Zr=o().Iterator)?void 0:Zr.prototype)||{};function oi(e){return e[Symbol.iterator]=ai,Object.assign(Object.create(ii),e)}function ai(){return this}["Symbol","Map","Set"].forEach((function(e){void 0===o()[e]&&r("MobX requires global '"+e+"' to be available or polyfilled")})),"object"==typeof __MOBX_DEVTOOLS_GLOBAL_HOOK__&&__MOBX_DEVTOOLS_GLOBAL_HOOK__.injectMobx({spy:Bt,extras:{getDebugName:Jr},$mobx:W})},275055:(e,t,n)=>{var r=n(875604),i=Symbol.for("react.element"),o=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,s=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,n){var r,o={},c=null,l=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(l=t.ref),t)a.call(t,r)&&!u.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===o[r]&&(o[r]=t[r]);return{$$typeof:i,type:e,key:c,ref:l,props:o,_owner:s.current}}t.Fragment=o,t.jsx=c,t.jsxs=c},473821:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),u=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),l=Symbol.for("react.suspense"),f=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),_=Symbol.iterator;var v={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},d=Object.assign,p={};function b(e,t,n){this.props=e,this.context=t,this.refs=p,this.updater=n||v}function y(){}function g(e,t,n){this.props=e,this.context=t,this.refs=p,this.updater=n||v}b.prototype.isReactComponent={},b.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},b.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=b.prototype;var m=g.prototype=new y;m.constructor=g,d(m,b.prototype),m.isPureReactComponent=!0;var O=Array.isArray,w=Object.prototype.hasOwnProperty,S={current:null},A={key:!0,ref:!0,__self:!0,__source:!0};function k(e,t,r){var i,o={},a=null,s=null;if(null!=t)for(i in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(a=""+t.key),t)w.call(t,i)&&!A.hasOwnProperty(i)&&(o[i]=t[i]);var u=arguments.length-2;if(1===u)o.children=r;else if(1<u){for(var c=Array(u),l=0;l<u;l++)c[l]=arguments[l+2];o.children=c}if(e&&e.defaultProps)for(i in u=e.defaultProps)void 0===o[i]&&(o[i]=u[i]);return{$$typeof:n,type:e,key:a,ref:s,props:o,_owner:S.current}}function j(e){return"object"==typeof e&&null!==e&&e.$$typeof===n}var E=/\/+/g;function x(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function P(e,t,i,o,a){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var u=!1;if(null===e)u=!0;else switch(s){case"string":case"number":u=!0;break;case"object":switch(e.$$typeof){case n:case r:u=!0}}if(u)return a=a(u=e),e=""===o?"."+x(u,0):o,O(a)?(i="",null!=e&&(i=e.replace(E,"$&/")+"/"),P(a,t,i,"",(function(e){return e}))):null!=a&&(j(a)&&(a=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(a,i+(!a.key||u&&u.key===a.key?"":(""+a.key).replace(E,"$&/")+"/")+e)),t.push(a)),1;if(u=0,o=""===o?".":o+":",O(e))for(var c=0;c<e.length;c++){var l=o+x(s=e[c],c);u+=P(s,t,i,l,a)}else if(l=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=_&&e[_]||e["@@iterator"])?e:null}(e),"function"==typeof l)for(e=l.call(e),c=0;!(s=e.next()).done;)u+=P(s=s.value,t,i,l=o+x(s,c++),a);else if("object"===s)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return u}function R(e,t,n){if(null==e)return e;var r=[],i=0;return P(e,r,"","",(function(e){return t.call(n,e,i++)})),r}function C(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var T={current:null},V={transition:null},M={ReactCurrentDispatcher:T,ReactCurrentBatchConfig:V,ReactCurrentOwner:S};function D(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:R,forEach:function(e,t,n){R(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return R(e,(function(){t++})),t},toArray:function(e){return R(e,(function(e){return e}))||[]},only:function(e){if(!j(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=b,t.Fragment=i,t.Profiler=a,t.PureComponent=g,t.StrictMode=o,t.Suspense=l,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=M,t.act=D,t.cloneElement=function(e,t,r){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var i=d({},e.props),o=e.key,a=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(a=t.ref,s=S.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(c in t)w.call(t,c)&&!A.hasOwnProperty(c)&&(i[c]=void 0===t[c]&&void 0!==u?u[c]:t[c])}var c=arguments.length-2;if(1===c)i.children=r;else if(1<c){u=Array(c);for(var l=0;l<c;l++)u[l]=arguments[l+2];i.children=u}return{$$typeof:n,type:e.type,key:o,ref:a,props:i,_owner:s}},t.createContext=function(e){return(e={$$typeof:u,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:s,_context:e},e.Consumer=e},t.createElement=k,t.createFactory=function(e){var t=k.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=j,t.lazy=function(e){return{$$typeof:h,_payload:{_status:-1,_result:e},_init:C}},t.memo=function(e,t){return{$$typeof:f,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=V.transition;V.transition={};try{e()}finally{V.transition=t}},t.unstable_act=D,t.useCallback=function(e,t){return T.current.useCallback(e,t)},t.useContext=function(e){return T.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return T.current.useDeferredValue(e)},t.useEffect=function(e,t){return T.current.useEffect(e,t)},t.useId=function(){return T.current.useId()},t.useImperativeHandle=function(e,t,n){return T.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return T.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return T.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return T.current.useMemo(e,t)},t.useReducer=function(e,t,n){return T.current.useReducer(e,t,n)},t.useRef=function(e){return T.current.useRef(e)},t.useState=function(e){return T.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return T.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return T.current.useTransition()},t.version="18.3.1"},875604:(e,t,n)=>{e.exports=n(473821)},443763:(e,t,n)=>{e.exports=n(275055)}}]);
//# sourceMappingURL=sourcemaps/6ac27b34c2f02745.i3d79q.vendor.js.map
<?php

namespace PrayerTimes\Services;

/**
 * Email Service - Handles email sending
 */
class EmailService
{
    private $fromEmail;
    private $fromName;
    private $toEmail;

    public function __construct()
    {
        $this->fromEmail = $_ENV['MAIL_FROM_EMAIL'] ?? '<EMAIL>';
        $this->fromName = $_ENV['MAIL_FROM_NAME'] ?? 'Prayer Times App';
        $this->toEmail = $_ENV['MAIL_TO_EMAIL'] ?? '<EMAIL>';
    }

    /**
     * Send contact form email
     */
    public function sendContactEmail($data)
    {
        $subject = 'Contact Form: ' . $data['subject'];
        $message = $this->buildContactEmailMessage($data);
        $headers = $this->buildEmailHeaders($data['email'], $data['name']);

        // For development, just log the email instead of sending
        if (($_ENV['APP_ENV'] ?? 'production') === 'development') {
            return $this->logEmail($subject, $message, $headers);
        }

        return mail($this->toEmail, $subject, $message, $headers);
    }

    /**
     * Build contact email message
     */
    private function buildContactEmailMessage($data)
    {
        return "
New contact form submission from Prayer Times App

Name: {$data['name']}
Email: {$data['email']}
Subject: {$data['subject']}

Message:
{$data['message']}

---
Sent from Prayer Times App Contact Form
Time: " . date('Y-m-d H:i:s') . "
IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown') . "
";
    }

    /**
     * Build email headers
     */
    private function buildEmailHeaders($replyEmail, $replyName)
    {
        return implode("\r\n", [
            "From: {$this->fromName} <{$this->fromEmail}>",
            "Reply-To: {$replyName} <{$replyEmail}>",
            "Content-Type: text/plain; charset=UTF-8",
            "X-Mailer: Prayer Times App"
        ]);
    }

    /**
     * Log email for development
     */
    private function logEmail($subject, $message, $headers)
    {
        $logEntry = "
=== EMAIL LOG ===
Date: " . date('Y-m-d H:i:s') . "
To: {$this->toEmail}
Subject: {$subject}
Headers: {$headers}

Message:
{$message}

=== END EMAIL ===

";

        $logFile = APP_ROOT . '/logs/emails.log';
        $logDir = dirname($logFile);

        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        return file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX) !== false;
    }

    /**
     * Send notification email to admin
     */
    public function sendNotificationEmail($subject, $message)
    {
        $headers = "From: {$this->fromName} <{$this->fromEmail}>\r\n";
        $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";

        if (($_ENV['APP_ENV'] ?? 'production') === 'development') {
            return $this->logEmail($subject, $message, $headers);
        }

        return mail($this->toEmail, $subject, $message, $headers);
    }
}

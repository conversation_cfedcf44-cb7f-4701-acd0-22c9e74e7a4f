* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.loading-container {
    text-align: center;
    color: white;
    max-width: 400px;
    width: 90%;
    padding: 2rem;
}

.logo-container {
    margin-bottom: 3rem;
    animation: fadeInUp 1s ease-out;
}

.mosque-logo {
    margin-bottom: 1.5rem;
    animation: float 3s ease-in-out infinite;
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.3));
}

.app-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: glow 2s ease-in-out infinite alternate;
}

.app-subtitle {
    font-size: 1.1rem;
    font-weight: 300;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.loading-spinner {
    margin: 2rem 0;
    animation: fadeInUp 1s ease-out 0.5s both;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #FFD700;
    border-radius: 50%;
    margin: 0 auto;
    animation: spin 1s linear infinite;
}

.loading-text {
    margin: 2rem 0;
    animation: fadeInUp 1s ease-out 1s both;
}

#loading-message {
    font-size: 1rem;
    font-weight: 400;
    opacity: 0.8;
    animation: pulse 2s ease-in-out infinite;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin-top: 2rem;
    animation: fadeInUp 1s ease-out 1.5s both;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #FFD700, #FFA500);
    border-radius: 2px;
    width: 0%;
    animation: progress 3s ease-out forwards;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
}

@keyframes glow {
    from {
        text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
    }
    to {
        text-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
    }
}

@keyframes progress {
    0% {
        width: 0%;
    }
    100% {
        width: 100%;
    }
}

/* Responsive design */
@media (max-width: 480px) {
    .app-title {
        font-size: 2rem;
    }
    
    .mosque-logo svg {
        width: 100px;
        height: 100px;
    }
    
    .loading-container {
        padding: 1.5rem;
    }
}

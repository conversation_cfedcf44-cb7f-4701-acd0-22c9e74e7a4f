<!DOCTYPE html>
<!-- saved from url=(0140)https://bchzb0wbwgz8xsgs.canva-hosted-embed.com/codelet/AAEAEGJjaHpiMHdid2d6OHhzZ3MAAAAAAZcchEJaUdGMKz9gp-wRIjQzYs0FbAs-ExdRDokhHICx0vEuUOU/ -->
<html lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prayer Times App</title>
    <script src="./saved_resource"></script>
    <link href="./css2" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
        }
        .prayer-card {
            transition: all 0.3s ease;
        }
        .prayer-card:hover {
            transform: translateY(-5px);
        }
        .next-prayer {
            background: linear-gradient(135deg, #43cea2 0%, #185a9d 100%);
        }
        .mosque-dome {
            filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
        }
        .compass-container {
            position: relative;
            width: 150px;
            height: 150px;
            margin: 0 auto;
        }
        .compass {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            position: relative;
            transition: transform 1s ease;
        }
        .compass-needle {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 4px;
            height: 60px;
            background: linear-gradient(to bottom, #e74c3c 50%, #fff 50%);
            transform-origin: center 75%;
            transform: translate(-50%, -75%);
            z-index: 10;
            border-radius: 2px;
        }
        .compass-center {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 15px;
            height: 15px;
            background: #fff;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 20;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        }
        .compass-marking {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
        }
        .compass-marking::before {
            content: '';
            position: absolute;
            top: 5px;
            left: 50%;
            width: 2px;
            height: 10px;
            background: #fff;
            transform: translateX(-50%);
        }
        .compass-marking:nth-child(1) { transform: rotate(0deg); }
        .compass-marking:nth-child(2) { transform: rotate(90deg); }
        .compass-marking:nth-child(3) { transform: rotate(180deg); }
        .compass-marking:nth-child(4) { transform: rotate(270deg); }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
<style>*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/* ! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com */*,::after,::before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::after,::before{--tw-content:''}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.container{width:100%}@media (min-width: 640px){.container{max-width:640px}}@media (min-width: 768px){.container{max-width:768px}}@media (min-width: 1024px){.container{max-width:1024px}}@media (min-width: 1280px){.container{max-width:1280px}}@media (min-width: 1536px){.container{max-width:1536px}}.mx-auto{margin-left:auto;margin-right:auto}.mb-1{margin-bottom:0.25rem}.mb-2{margin-bottom:0.5rem}.mb-3{margin-bottom:0.75rem}.mb-4{margin-bottom:1rem}.mb-8{margin-bottom:2rem}.mt-1{margin-top:0.25rem}.mt-3{margin-top:0.75rem}.mt-4{margin-top:1rem}.block{display:block}.flex{display:flex}.grid{display:grid}.h-1{height:0.25rem}.h-2{height:0.5rem}.h-full{height:100%}.w-full{width:100%}.grid-cols-1{grid-template-columns:repeat(1, minmax(0, 1fr))}.flex-col{flex-direction:column}.items-center{align-items:center}.justify-between{justify-content:space-between}.gap-6{gap:1.5rem}.overflow-hidden{overflow:hidden}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:0.5rem}.rounded-xl{border-radius:0.75rem}.border{border-width:1px}.border-4{border-width:4px}.border-white\/30{border-color:rgb(255 255 255 / 0.3)}.bg-blue-400{--tw-bg-opacity:1;background-color:rgb(96 165 250 / var(--tw-bg-opacity, 1))}.bg-blue-500{--tw-bg-opacity:1;background-color:rgb(59 130 246 / var(--tw-bg-opacity, 1))}.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.bg-white\/10{background-color:rgb(255 255 255 / 0.1)}.bg-white\/20{background-color:rgb(255 255 255 / 0.2)}.p-6{padding:1.5rem}.px-4{padding-left:1rem;padding-right:1rem}.py-2{padding-top:0.5rem;padding-bottom:0.5rem}.py-8{padding-top:2rem;padding-bottom:2rem}.text-center{text-align:center}.text-2xl{font-size:1.5rem;line-height:2rem}.text-3xl{font-size:1.875rem;line-height:2.25rem}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-sm{font-size:0.875rem;line-height:1.25rem}.text-xl{font-size:1.25rem;line-height:1.75rem}.font-bold{font-weight:700}.font-medium{font-weight:500}.font-semibold{font-weight:600}.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.opacity-70{opacity:0.7}.opacity-80{opacity:0.8}.shadow-lg{--tw-shadow:0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.backdrop-blur-md{--tw-backdrop-blur:blur(12px);-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)}.transition{transition-property:color, background-color, border-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-text-decoration-color, -webkit-backdrop-filter;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-text-decoration-color, -webkit-backdrop-filter;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.hover\:bg-blue-600:hover{--tw-bg-opacity:1;background-color:rgb(37 99 235 / var(--tw-bg-opacity, 1))}@media (min-width: 768px){.md\:grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}}@media (min-width: 1024px){.lg\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}}</style></head>
<body class="text-white">
    <div class="container mx-auto px-4 py-8">
        <header class="flex flex-col items-center mb-8">
            <div class="mosque-dome mb-4">
                <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 8H22L12 2Z" fill="#FFD700"></path>
                    <path d="M4 10V20H20V10H4Z" fill="#FFD700"></path>
                    <path d="M12 5L5 9H19L12 5Z" fill="#FFD700"></path>
                    <path d="M10 10V20H14V10H10Z" fill="#FFD700"></path>
                    <circle cx="12" cy="6" r="1" fill="#FFD700"></circle>
                </svg>
            </div>
            <h1 class="text-3xl font-bold text-center">Prayer Times</h1>
            <p id="current-date" class="text-lg opacity-80 mt-1">Wednesday, May 28, 2025</p>
            <p id="location" class="text-md opacity-70"></p>
        </header>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div id="fajr" class="prayer-card bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg">
                <div class="flex justify-between items-center mb-3">
                    <h2 class="text-xl font-semibold">Fajr</h2>
                    <span class="prayer-time text-lg">05:30</span>
                </div>
                <div class="h-1 w-full bg-white/20 rounded-full overflow-hidden">
                    <div class="prayer-progress h-full bg-blue-400 rounded-full" style="width: 0%"></div>
                </div>
            </div>
            
            <div id="dhuhr" class="prayer-card bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg">
                <div class="flex justify-between items-center mb-3">
                    <h2 class="text-xl font-semibold">Dhuhr</h2>
                    <span class="prayer-time text-lg">12:30</span>
                </div>
                <div class="h-1 w-full bg-white/20 rounded-full overflow-hidden">
                    <div class="prayer-progress h-full bg-blue-400 rounded-full" style="width: 0%"></div>
                </div>
            </div>
            
            <div id="asr" class="prayer-card bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg">
                <div class="flex justify-between items-center mb-3">
                    <h2 class="text-xl font-semibold">Asr</h2>
                    <span class="prayer-time text-lg">15:45</span>
                </div>
                <div class="h-1 w-full bg-white/20 rounded-full overflow-hidden">
                    <div class="prayer-progress h-full bg-blue-400 rounded-full" style="width: 0%"></div>
                </div>
            </div>
            
            <div id="maghrib" class="prayer-card bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg next-prayer pulse">
                <div class="flex justify-between items-center mb-3">
                    <h2 class="text-xl font-semibold">Maghrib</h2>
                    <span class="prayer-time text-lg">18:15</span>
                </div>
                <div class="h-1 w-full bg-white/20 rounded-full overflow-hidden">
                    <div class="prayer-progress h-full bg-blue-400 rounded-full" style="width: 0%"></div>
                </div>
            </div>
            
            <div id="isha" class="prayer-card bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg">
                <div class="flex justify-between items-center mb-3">
                    <h2 class="text-xl font-semibold">Isha</h2>
                    <span class="prayer-time text-lg">19:45</span>
                </div>
                <div class="h-1 w-full bg-white/20 rounded-full overflow-hidden">
                    <div class="prayer-progress h-full bg-blue-400 rounded-full" style="width: 0%"></div>
                </div>
            </div>
            
            <div id="next-prayer-container" class="prayer-card rounded-xl p-6 shadow-lg">
                <h2 class="text-xl font-semibold mb-2">Next Prayer</h2>
                <div class="flex justify-between items-center">
                    <span id="next-prayer-name" class="text-2xl font-bold">Maghrib</span>
                    <span id="next-prayer-time" class="text-xl">18:15</span>
                </div>
                <div class="mt-3">
                    <div class="flex justify-between text-sm opacity-80 mb-1">
                        <span>Time remaining</span>
                        <span id="countdown">00:23:41</span>
                    </div>
                    <div class="h-2 w-full bg-white/20 rounded-full overflow-hidden">
                        <div id="countdown-progress" class="h-full bg-white rounded-full" style="width: 86.8426%;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg mb-8">
            <h2 class="text-xl font-semibold mb-4 text-center">Qibla Direction</h2>
            <div class="compass-container">
                <div class="compass bg-white/20 backdrop-blur-md rounded-full border-4 border-white/30">
                    <div class="compass-marking"></div>
                    <div class="compass-marking"></div>
                    <div class="compass-marking"></div>
                    <div class="compass-marking"></div>
                    <div class="compass-needle"></div>
                    <div class="compass-center"></div>
                </div>
            </div>
            <div class="text-center mt-4">
                <p id="qibla-direction" class="text-lg font-medium"></p>
                <button id="find-qibla" class="mt-3 bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition">
                    Find Qibla Direction
                </button>
            </div>
        </div>

        <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg">
            <h2 class="text-xl font-semibold mb-4">Settings</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium mb-2">Calculation Method</label>
                    <select id="calculation-method" class="w-full bg-white/20 backdrop-blur-md border border-white/30 rounded-lg px-4 py-2 text-white">
                        <option value="MWL">Muslim World League</option>
                        <option value="ISNA">Islamic Society of North America</option>
                        <option value="Egypt">Egyptian General Authority</option>
                        <option value="Makkah">Umm al-Qura University, Makkah</option>
                        <option value="Karachi">University of Islamic Sciences, Karachi</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Asr Method</label>
                    <select id="asr-method" class="w-full bg-white/20 backdrop-blur-md border border-white/30 rounded-lg px-4 py-2 text-white">
                        <option value="Standard">Standard (Shafi'i, Maliki, Hanbali)</option>
                        <option value="Hanafi">Hanafi</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Time Format</label>
                    <select id="time-format" class="w-full bg-white/20 backdrop-blur-md border border-white/30 rounded-lg px-4 py-2 text-white">
                        <option value="24h">24-hour</option>
                        <option value="12h">12-hour</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Location</label>
                    <button id="get-location" class="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition">
                        Use Current Location
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Default prayer times (will be replaced with API data)
            let prayerTimes = {
                fajr: '05:30',
                dhuhr: '12:30',
                asr: '15:45',
                maghrib: '18:15',
                isha: '19:45'
            };
            
            let settings = {
                calculationMethod: 'MWL',
                asrMethod: 'Standard',
                timeFormat: '24h',
                latitude: null,
                longitude: null
            };
            
            // Set current date
            const today = new Date();
            document.getElementById('current-date').textContent = today.toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            });
            
            // Initialize UI
            updateUI();
            
            // Get user's location
            document.getElementById('get-location').addEventListener('click', function() {
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(
                        function(position) {
                            settings.latitude = position.coords.latitude;
                            settings.longitude = position.coords.longitude;
                            
                            // Get location name
                            fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${settings.latitude}&lon=${settings.longitude}`)
                                .then(response => response.json())
                                .then(data => {
                                    const city = data.address.city || data.address.town || data.address.village || '';
                                    const country = data.address.country || '';
                                    document.getElementById('location').textContent = `${city}, ${country}`;
                                })
                                .catch(error => {
                                    console.error('Error getting location name:', error);
                                    document.getElementById('location').textContent = `Lat: ${settings.latitude.toFixed(4)}, Long: ${settings.longitude.toFixed(4)}`;
                                });
                            
                            // Get prayer times based on location
                            getPrayerTimes();
                        },
                        function(error) {
                            console.error('Error getting location:', error);
                            alert('Unable to get your location. Using default prayer times.');
                        }
                    );
                } else {
                    alert('Geolocation is not supported by your browser. Using default prayer times.');
                }
            });
            
            // Handle settings changes
            document.getElementById('calculation-method').addEventListener('change', function() {
                settings.calculationMethod = this.value;
                getPrayerTimes();
            });
            
            document.getElementById('asr-method').addEventListener('change', function() {
                settings.asrMethod = this.value;
                getPrayerTimes();
            });
            
            document.getElementById('time-format').addEventListener('change', function() {
                settings.timeFormat = this.value;
                updateUI();
            });
            
            // Find Qibla direction
            document.getElementById('find-qibla').addEventListener('click', function() {
                if (settings.latitude && settings.longitude) {
                    if (window.DeviceOrientationEvent && typeof DeviceOrientationEvent.requestPermission === 'function') {
                        // iOS 13+ requires permission
                        DeviceOrientationEvent.requestPermission()
                            .then(response => {
                                if (response === 'granted') {
                                    enableCompass();
                                } else {
                                    alert('Permission to access device orientation was denied');
                                }
                            })
                            .catch(error => {
                                console.error('Error requesting device orientation permission:', error);
                                calculateStaticQibla();
                            });
                    } else if (window.DeviceOrientationEvent) {
                        // Other browsers
                        enableCompass();
                    } else {
                        // Fallback for browsers that don't support DeviceOrientation
                        calculateStaticQibla();
                    }
                } else {
                    alert('Please enable location first to find Qibla direction');
                    document.getElementById('get-location').click();
                }
            });
            
            function enableCompass() {
                window.addEventListener('deviceorientation', handleOrientation);
                alert('Rotate your device to find Qibla direction. The red part of the needle points to Qibla.');
            }
            
            function handleOrientation(event) {
                const compass = document.querySelector('.compass');
                let alpha = event.alpha; // compass direction in degrees (0-360)
                
                if (alpha !== null) {
                    // Calculate Qibla direction
                    const qiblaDirection = calculateQiblaDirection(settings.latitude, settings.longitude);
                    
                    // Adjust compass to point to Qibla
                    const compassRotation = 360 - alpha; // Invert direction for compass rotation
                    const needleRotation = qiblaDirection;
                    
                    compass.style.transform = `rotate(${compassRotation}deg)`;
                    document.querySelector('.compass-needle').style.transform = `translate(-50%, -75%) rotate(${needleRotation}deg)`;
                    
                    document.getElementById('qibla-direction').textContent = `Qibla is at ${Math.round(qiblaDirection)}° from North`;
                }
            }
            
            function calculateStaticQibla() {
                const qiblaDirection = calculateQiblaDirection(settings.latitude, settings.longitude);
                document.querySelector('.compass-needle').style.transform = `translate(-50%, -75%) rotate(${qiblaDirection}deg)`;
                document.getElementById('qibla-direction').textContent = `Qibla is at ${Math.round(qiblaDirection)}° from North`;
            }
            
            function calculateQiblaDirection(latitude, longitude) {
                // Mecca coordinates
                const meccaLat = 21.4225;
                const meccaLong = 39.8262;
                
                // Convert to radians
                const lat1 = latitude * (Math.PI / 180);
                const lat2 = meccaLat * (Math.PI / 180);
                const longDiff = (meccaLong - longitude) * (Math.PI / 180);
                
                // Calculate Qibla direction
                const y = Math.sin(longDiff);
                const x = Math.cos(lat1) * Math.tan(lat2) - Math.sin(lat1) * Math.cos(longDiff);
                let qibla = Math.atan2(y, x) * (180 / Math.PI);
                
                // Normalize to 0-360
                if (qibla < 0) {
                    qibla += 360;
                }
                
                return qibla;
            }
            
            function getPrayerTimes() {
                if (!settings.latitude || !settings.longitude) {
                    // Use default prayer times if location is not available
                    updateUI();
                    return;
                }
                
                const date = new Date();
                const year = date.getFullYear();
                const month = date.getMonth() + 1;
                const day = date.getDate();
                
                // In a real app, you would use an API like Aladhan API
                // This is a simplified simulation
                simulatePrayerTimesAPI(year, month, day);
            }
            
            function simulatePrayerTimesAPI(year, month, day) {
                // Simulate API response with slightly randomized times based on current time
                const now = new Date();
                const baseHour = now.getHours();
                
                // Generate prayer times around the current time for demo purposes
                prayerTimes = {
                    fajr: formatTime(baseHour - 8 + Math.random() * 2),
                    dhuhr: formatTime(12 + Math.random()),
                    asr: formatTime(15 + Math.random() * 2),
                    maghrib: formatTime(18 + Math.random()),
                    isha: formatTime(19 + Math.random() * 2)
                };
                
                updateUI();
            }
            
            function formatTime(hour) {
                const h = Math.floor(hour % 24);
                const m = Math.floor(Math.random() * 59);
                return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`;
            }
            
            function updateUI() {
                // Update prayer time displays
                document.querySelector('#fajr .prayer-time').textContent = formatTimeDisplay(prayerTimes.fajr);
                document.querySelector('#dhuhr .prayer-time').textContent = formatTimeDisplay(prayerTimes.dhuhr);
                document.querySelector('#asr .prayer-time').textContent = formatTimeDisplay(prayerTimes.asr);
                document.querySelector('#maghrib .prayer-time').textContent = formatTimeDisplay(prayerTimes.maghrib);
                document.querySelector('#isha .prayer-time').textContent = formatTimeDisplay(prayerTimes.isha);
                
                // Find next prayer
                updateNextPrayer();
                
                // Start countdown timer
                startCountdown();
            }
            
            function formatTimeDisplay(timeStr) {
                if (settings.timeFormat === '12h') {
                    const [hours, minutes] = timeStr.split(':');
                    const h = parseInt(hours);
                    const ampm = h >= 12 ? 'PM' : 'AM';
                    const hour12 = h % 12 || 12;
                    return `${hour12}:${minutes} ${ampm}`;
                }
                return timeStr;
            }
            
            function parseTime(timeStr) {
                const [hours, minutes] = timeStr.split(':');
                const date = new Date();
                date.setHours(parseInt(hours));
                date.setMinutes(parseInt(minutes));
                date.setSeconds(0);
                return date;
            }
            
            function updateNextPrayer() {
                const now = new Date();
                const prayers = [
                    { name: 'Fajr', time: parseTime(prayerTimes.fajr) },
                    { name: 'Dhuhr', time: parseTime(prayerTimes.dhuhr) },
                    { name: 'Asr', time: parseTime(prayerTimes.asr) },
                    { name: 'Maghrib', time: parseTime(prayerTimes.maghrib) },
                    { name: 'Isha', time: parseTime(prayerTimes.isha) }
                ];
                
                // Reset all prayer cards
                document.querySelectorAll('.prayer-card').forEach(card => {
                    card.classList.remove('next-prayer', 'pulse');
                    card.style.background = '';
                });
                
                // Find next prayer
                let nextPrayer = null;
                for (const prayer of prayers) {
                    if (prayer.time > now) {
                        nextPrayer = prayer;
                        break;
                    }
                }
                
                // If no next prayer today, it's Fajr tomorrow
                if (!nextPrayer) {
                    nextPrayer = { name: 'Fajr (Tomorrow)', time: new Date(now) };
                    nextPrayer.time.setDate(nextPrayer.time.getDate() + 1);
                    nextPrayer.time.setHours(parseInt(prayerTimes.fajr.split(':')[0]));
                    nextPrayer.time.setMinutes(parseInt(prayerTimes.fajr.split(':')[1]));
                }
                
                // Update next prayer display
                document.getElementById('next-prayer-name').textContent = nextPrayer.name;
                document.getElementById('next-prayer-time').textContent = formatTimeDisplay(
                    `${nextPrayer.time.getHours().toString().padStart(2, '0')}:${nextPrayer.time.getMinutes().toString().padStart(2, '0')}`
                );
                
                // Highlight the next prayer card
                const prayerCard = document.getElementById(nextPrayer.name.toLowerCase().split(' ')[0]);
                if (prayerCard) {
                    prayerCard.classList.add('next-prayer', 'pulse');
                }
                
                return nextPrayer;
            }
            
            function startCountdown() {
                const nextPrayer = updateNextPrayer();
                
                // Update countdown every second
                setInterval(() => {
                    const now = new Date();
                    const diff = nextPrayer.time - now;
                    
                    if (diff <= 0) {
                        // Time for prayer, update next prayer
                        updateNextPrayer();
                        return;
                    }
                    
                    // Calculate hours, minutes, seconds
                    const hours = Math.floor(diff / (1000 * 60 * 60));
                    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
                    
                    // Update countdown display
                    document.getElementById('countdown').textContent = 
                        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                    
                    // Calculate progress percentage (inverted)
                    const totalSecondsInDay = 24 * 60 * 60;
                    const remainingSeconds = hours * 3600 + minutes * 60 + seconds;
                    const progressPercentage = 100 - (remainingSeconds / (3 * 60 * 60)) * 100; // Max 3 hours countdown shown
                    
                    document.getElementById('countdown-progress').style.width = `${Math.min(100, Math.max(0, progressPercentage))}%`;
                }, 1000);
            }
            
            // Trigger location request on load
            setTimeout(() => {
                document.getElementById('get-location').click();
            }, 1000);
        });
    </script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'946e8de160c4e3eb',t:'MTc0ODQ0MzcyMC4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script><iframe height="1" width="1" style="position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;" src="./saved_resource.html"></iframe>

</body></html>
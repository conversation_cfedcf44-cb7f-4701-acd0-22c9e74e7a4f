<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prayer Times App - Home</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="/assets/css/style.css">
    <link rel="stylesheet" href="/assets/css/home.css">
</head>
<body class="text-white">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white/10 backdrop-blur-md z-50 border-b border-white/20">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <svg width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 8H22L12 2Z" fill="#FFD700"></path>
                    <path d="M4 10V20H20V10H4Z" fill="#FFD700"></path>
                    <path d="M12 5L5 9H19L12 5Z" fill="#FFD700"></path>
                    <path d="M10 10V20H14V10H10Z" fill="#FFD700"></path>
                    <circle cx="12" cy="6" r="1" fill="#FFD700"></circle>
                </svg>
                <span class="text-lg font-semibold">Prayer Times</span>
            </div>
            <div class="flex space-x-6">
                <a href="/" class="text-yellow-300 font-semibold border-b-2 border-yellow-300 pb-1">Home</a>
                <a href="/prayer-times" class="text-white hover:text-yellow-300 transition duration-300">Prayer Times</a>
                <a href="/contact" class="text-white hover:text-yellow-300 transition duration-300">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="min-h-screen flex items-center justify-center relative overflow-hidden">
        <!-- Background Animation -->
        <div class="absolute inset-0 opacity-10">
            <div class="floating-mosque absolute top-20 left-10 animate-float">
                <svg width="60" height="60" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 8H22L12 2Z" fill="#FFD700"></path>
                    <path d="M4 10V20H20V10H4Z" fill="#FFD700"></path>
                </svg>
            </div>
            <div class="floating-mosque absolute top-40 right-20 animate-float-delayed">
                <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 8H22L12 2Z" fill="#FFD700"></path>
                    <path d="M4 10V20H20V10H4Z" fill="#FFD700"></path>
                </svg>
            </div>
        </div>

        <div class="container mx-auto px-4 py-8 mt-16 text-center">
            <!-- Main Logo and Title -->
            <div class="hero-content">
                <div class="mosque-logo mb-8 animate-float">
                    <svg width="150" height="150" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="mx-auto">
                        <path d="M12 2L2 8H22L12 2Z" fill="#FFD700"></path>
                        <path d="M4 10V20H20V10H4Z" fill="#FFD700"></path>
                        <path d="M12 5L5 9H19L12 5Z" fill="#FFD700"></path>
                        <path d="M10 10V20H14V10H10Z" fill="#FFD700"></path>
                        <circle cx="12" cy="6" r="1" fill="#FFD700"></circle>
                    </svg>
                </div>

                <h1 class="text-6xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-yellow-400 via-yellow-300 to-yellow-500 bg-clip-text text-transparent animate-glow">
                    Prayer Times
                </h1>

                <p class="text-xl md:text-2xl mb-8 opacity-90 max-w-2xl mx-auto leading-relaxed">
                    Your spiritual companion for accurate prayer times, Qibla direction, and Islamic guidance
                </p>

                <!-- Feature Cards -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 max-w-4xl mx-auto">
                    <div class="feature-card bg-white/10 backdrop-blur-md rounded-xl p-6 hover:bg-white/20 transition duration-300">
                        <div class="text-yellow-400 mb-4">
                            <svg width="48" height="48" fill="currentColor" viewBox="0 0 20 20" class="mx-auto">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold mb-2">Accurate Times</h3>
                        <p class="text-sm opacity-80">Precise prayer times based on your location with multiple calculation methods</p>
                    </div>

                    <div class="feature-card bg-white/10 backdrop-blur-md rounded-xl p-6 hover:bg-white/20 transition duration-300">
                        <div class="text-yellow-400 mb-4">
                            <svg width="48" height="48" fill="currentColor" viewBox="0 0 20 20" class="mx-auto">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold mb-2">Qibla Direction</h3>
                        <p class="text-sm opacity-80">Find the exact direction to Mecca from anywhere in the world</p>
                    </div>

                    <div class="feature-card bg-white/10 backdrop-blur-md rounded-xl p-6 hover:bg-white/20 transition duration-300">
                        <div class="text-yellow-400 mb-4">
                            <svg width="48" height="48" fill="currentColor" viewBox="0 0 20 20" class="mx-auto">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold mb-2">Responsive Design</h3>
                        <p class="text-sm opacity-80">Beautiful interface that works perfectly on all devices</p>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                    <a href="/prayer-times" class="btn-primary bg-gradient-to-r from-yellow-400 to-yellow-600 text-gray-900 font-semibold py-4 px-8 rounded-lg hover:from-yellow-500 hover:to-yellow-700 transition duration-300 transform hover:scale-105 shadow-lg">
                        View Prayer Times
                    </a>
                    <a href="/contact" class="btn-secondary bg-white/20 hover:bg-white/30 text-white font-medium py-4 px-8 rounded-lg transition duration-300 border border-white/30 hover:border-white/50">
                        Get Support
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Info Section -->
    <section class="py-16 bg-white/5 backdrop-blur-sm">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold mb-4">Why Choose Our Prayer Times App?</h2>
                <p class="text-lg opacity-80 max-w-2xl mx-auto">Built with precision and care for the Muslim community worldwide</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="text-yellow-400 text-4xl font-bold mb-2">99.9%</div>
                    <div class="text-sm opacity-80">Accuracy Rate</div>
                </div>
                <div class="text-center">
                    <div class="text-yellow-400 text-4xl font-bold mb-2">24/7</div>
                    <div class="text-sm opacity-80">Available</div>
                </div>
                <div class="text-center">
                    <div class="text-yellow-400 text-4xl font-bold mb-2">15+</div>
                    <div class="text-sm opacity-80">Calculation Methods</div>
                </div>
                <div class="text-center">
                    <div class="text-yellow-400 text-4xl font-bold mb-2">Global</div>
                    <div class="text-sm opacity-80">Coverage</div>
                </div>
            </div>
        </div>
    </section>

    <script src="/assets/js/home.js"></script>
</body>
</html>
<?php

declare(strict_types=1);

namespace PrayerTimes\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

/**
 * Prayer Times Service - Handles prayer times API calls
 */
class PrayerTimesService
{
    private Client $httpClient;
    private string $apiBaseUrl = 'https://api.aladhan.com/v1';

    public function __construct()
    {
        $this->httpClient = new Client([
            'timeout' => 10,
            'verify' => false // For development only
        ]);
    }

    /**
     * Get prayer times for a specific location and date
     */
    public function getPrayerTimes(float $latitude, float $longitude, int $method = 2, string $date = null): array
    {
        if (!$date) {
            $date = date('d-m-Y');
        }

        try {
            $response = $this->httpClient->get("{$this->apiBaseUrl}/timings/{$date}", [
                'query' => [
                    'latitude' => $latitude,
                    'longitude' => $longitude,
                    'method' => $method
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if ($data['code'] !== 200) {
                throw new \Exception('Failed to fetch prayer times from API');
            }

            return $this->formatPrayerTimes($data['data']);

        } catch (GuzzleException $e) {
            // Fallback to default times if API fails
            return $this->getDefaultPrayerTimes();
        }
    }

    /**
     * Format prayer times data
     */
    private function formatPrayerTimes(array $data): array
    {
        $timings = $data['timings'];
        
        return [
            'date' => $data['date']['readable'],
            'location' => [
                'city' => $data['meta']['timezone'] ?? 'Unknown',
                'country' => $data['meta']['timezone'] ?? 'Unknown'
            ],
            'timings' => [
                'Fajr' => $this->cleanTime($timings['Fajr']),
                'Sunrise' => $this->cleanTime($timings['Sunrise']),
                'Dhuhr' => $this->cleanTime($timings['Dhuhr']),
                'Asr' => $this->cleanTime($timings['Asr']),
                'Sunset' => $this->cleanTime($timings['Sunset']),
                'Maghrib' => $this->cleanTime($timings['Maghrib']),
                'Isha' => $this->cleanTime($timings['Isha']),
                'Imsak' => $this->cleanTime($timings['Imsak']),
                'Midnight' => $this->cleanTime($timings['Midnight'])
            ],
            'method' => $data['meta']['method']['name'] ?? 'Unknown'
        ];
    }

    /**
     * Clean time format (remove timezone info)
     */
    private function cleanTime(string $time): string
    {
        return explode(' ', $time)[0];
    }

    /**
     * Get default prayer times as fallback
     */
    private function getDefaultPrayerTimes(): array
    {
        return [
            'date' => date('l, F j, Y'),
            'location' => [
                'city' => 'Default',
                'country' => 'Location'
            ],
            'timings' => [
                'Fajr' => '05:30',
                'Sunrise' => '06:45',
                'Dhuhr' => '12:15',
                'Asr' => '15:45',
                'Sunset' => '18:30',
                'Maghrib' => '18:45',
                'Isha' => '20:15',
                'Imsak' => '05:20',
                'Midnight' => '00:15'
            ],
            'method' => 'Default Calculation'
        ];
    }

    /**
     * Get available calculation methods
     */
    public function getCalculationMethods(): array
    {
        return [
            1 => 'University of Islamic Sciences, Karachi',
            2 => 'Islamic Society of North America (ISNA)',
            3 => 'Muslim World League',
            4 => 'Umm Al-Qura University, Makkah',
            5 => 'Egyptian General Authority of Survey',
            7 => 'Institute of Geophysics, University of Tehran',
            8 => 'Gulf Region',
            9 => 'Kuwait',
            10 => 'Qatar',
            11 => 'Majlis Ugama Islam Singapura, Singapore',
            12 => 'Union Organization islamic de France',
            13 => 'Diyanet İşleri Başkanlığı, Turkey',
            14 => 'Spiritual Administration of Muslims of Russia'
        ];
    }
}

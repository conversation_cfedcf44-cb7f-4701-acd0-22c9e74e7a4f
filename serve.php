<?php

/**
 * Development Server Script
 *
 * This script helps run the PHP development server with proper routing.
 * Usage: php serve.php
 */

$host = 'localhost';
$port = 8000;
$publicDir = __DIR__ . '/public';

// Check if public directory exists
if (!is_dir($publicDir)) {
    echo "Error: Public directory not found at {$publicDir}\n";
    exit(1);
}

// Check if PHP version is compatible
if (version_compare(PHP_VERSION, '7.3.0', '<')) {
    echo "Error: PHP 7.3 or higher is required. Current version: " . PHP_VERSION . "\n";
    exit(1);
}

echo "Starting PHP development server...\n";
echo "Server: http://{$host}:{$port}\n";
echo "Document root: {$publicDir}\n";
echo "Press Ctrl+C to stop the server\n\n";

// Change to public directory
chdir($publicDir);

// Start the server
$command = sprintf(
    'php -S %s:%d -t %s',
    escapeshellarg($host),
    $port,
    escapeshellarg($publicDir)
);

passthru($command);

<?php

namespace PrayerTimes\Helpers;

/**
 * View Helper
 */
class View
{
    /**
     * Render a view with layout
     */
    public static function render($template, $data = [], $layout = true)
    {
        extract($data);
        
        if ($layout) {
            // Include header
            include APP_ROOT . '/src/Views/layouts/header.php';
        }
        
        // Include main template
        $templatePath = APP_ROOT . '/src/Views/' . $template . '.php';
        if (file_exists($templatePath)) {
            include $templatePath;
        } else {
            throw new \Exception("Template not found: {$template}");
        }
        
        if ($layout) {
            // Include footer
            include APP_ROOT . '/src/Views/layouts/footer.php';
        }
    }

    /**
     * Render partial view
     */
    public static function partial($template, $data = [])
    {
        extract($data);
        $templatePath = APP_ROOT . '/src/Views/partials/' . $template . '.php';
        
        if (file_exists($templatePath)) {
            include $templatePath;
        } else {
            throw new \Exception("Partial not found: {$template}");
        }
    }

    /**
     * Escape HTML
     */
    public static function escape($string)
    {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Generate URL
     */
    public static function url($path = '')
    {
        $baseUrl = Config::get('url', 'http://localhost:8000');
        return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
    }

    /**
     * Generate asset URL
     */
    public static function asset($path)
    {
        return '/assets/' . ltrim($path, '/');
    }

    /**
     * Check if current page matches
     */
    public static function isCurrentPage($page)
    {
        $currentPath = $_SERVER['REQUEST_URI'] ?? '/';
        $currentPath = strtok($currentPath, '?'); // Remove query string
        $currentPath = rtrim($currentPath, '/') ?: '/';
        
        $pagePath = '/' . ltrim($page, '/');
        $pagePath = rtrim($pagePath, '/') ?: '/';
        
        return $currentPath === $pagePath;
    }

    /**
     * Get navigation class
     */
    public static function navClass($page, $activeClass = 'active', $defaultClass = '')
    {
        return self::isCurrentPage($page) ? $activeClass : $defaultClass;
    }

    /**
     * Format date
     */
    public static function formatDate($date, $format = 'l, F j, Y')
    {
        if (is_string($date)) {
            $date = new \DateTime($date);
        }
        return $date->format($format);
    }

    /**
     * Format time
     */
    public static function formatTime($time, $format12Hour = true)
    {
        if (!$time) return '';
        
        $parts = explode(':', $time);
        if (count($parts) < 2) return $time;
        
        $hour = (int)$parts[0];
        $minute = $parts[1];
        
        if ($format12Hour) {
            $ampm = $hour >= 12 ? 'PM' : 'AM';
            $hour12 = $hour === 0 ? 12 : ($hour > 12 ? $hour - 12 : $hour);
            return "{$hour12}:{$minute} {$ampm}";
        } else {
            return sprintf('%02d:%s', $hour, $minute);
        }
    }

    /**
     * Include CSS file
     */
    public static function css($file)
    {
        echo '<link rel="stylesheet" href="' . self::asset("css/{$file}") . '">';
    }

    /**
     * Include JS file
     */
    public static function js($file)
    {
        echo '<script src="' . self::asset("js/{$file}") . '"></script>';
    }

    /**
     * Generate CSRF token
     */
    public static function csrfToken()
    {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }

    /**
     * Generate CSRF input field
     */
    public static function csrfField()
    {
        $token = self::csrfToken();
        $name = Config::get('security.csrf_token_name', '_token');
        return '<input type="hidden" name="' . $name . '" value="' . $token . '">';
    }
}

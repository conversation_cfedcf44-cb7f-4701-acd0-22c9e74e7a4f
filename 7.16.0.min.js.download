/*! @sentry/browser 7.16.0 (5386ce7) | https://github.com/getsentry/sentry-javascript */
var Sentry=function(t){const n=Object.prototype.toString;function e(t){switch(n.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return h(t,Error)}}function r(t,e){return n.call(t)===`[object ${e}]`}function i(t){return r(t,"ErrorEvent")}function s(t){return r(t,"DOMError")}function o(t){return r(t,"String")}function c(t){return null===t||"object"!=typeof t&&"function"!=typeof t}function u(t){return r(t,"Object")}function a(t){return"undefined"!=typeof Event&&h(t,Event)}function f(t){return Boolean(t&&t.then&&"function"==typeof t.then)}function h(t,n){try{return t instanceof n}catch(t){return!1}}function l(t){return t&&t.Math==Math?t:void 0}const d="object"==typeof globalThis&&l(globalThis)||"object"==typeof window&&l(window)||"object"==typeof self&&l(self)||"object"==typeof global&&l(global)||function(){return this}()||{};function p(t,n,e){const r=e||d,i=r.__SENTRY__=r.__SENTRY__||{};return i[t]||(i[t]=n())}const y=d;function m(t,n){try{let e=t;const r=5,i=80,s=[];let o=0,c=0;const u=" > ",a=u.length;let f;for(;e&&o++<r&&(f=v(e,n),!("html"===f||o>1&&c+s.length*a+f.length>=i));)s.push(f),c+=f.length,e=e.parentNode;return s.reverse().join(u)}catch(t){return"<unknown>"}}function v(t,n){const e=t,r=[];let i,s,c,u,a;if(!e||!e.tagName)return"";r.push(e.tagName.toLowerCase());const f=n&&n.length?n.filter((t=>e.getAttribute(t))).map((t=>[t,e.getAttribute(t)])):null;if(f&&f.length)f.forEach((t=>{r.push(`[${t[0]}="${t[1]}"]`)}));else if(e.id&&r.push(`#${e.id}`),i=e.className,i&&o(i))for(s=i.split(/\s+/),a=0;a<s.length;a++)r.push(`.${s[a]}`);const h=["type","name","title","alt"];for(a=0;a<h.length;a++)c=h[a],u=e.getAttribute(c),u&&r.push(`[${c}="${u}"]`);return r.join("")}class g extends Error{constructor(t,n="warn"){super(t),this.message=t,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=n}}const b=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function _(t,n=!1){const{host:e,path:r,pass:i,port:s,projectId:o,protocol:c,publicKey:u}=t;return`${c}://${u}${n&&i?`:${i}`:""}@${e}${s?`:${s}`:""}/${r?`${r}/`:r}${o}`}function w(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function E(t){return"string"==typeof t?function(t){const n=b.exec(t);if(!n)throw new g(`Invalid Sentry Dsn: ${t}`);const[e,r,i="",s,o="",c]=n.slice(1);let u="",a=c;const f=a.split("/");if(f.length>1&&(u=f.slice(0,-1).join("/"),a=f.pop()),a){const t=a.match(/^\d+/);t&&(a=t[0])}return w({host:s,pass:i,path:u,projectId:a,port:o,protocol:e,publicKey:r})}(t):w(t)}const S=["debug","info","warn","error","log","assert","trace"];function $(t,n=0){return"string"!=typeof t||0===n||t.length<=n?t:`${t.substr(0,n)}...`}function x(t,n){if(!Array.isArray(t))return"";const e=[];for(let n=0;n<t.length;n++){const r=t[n];try{e.push(String(r))}catch(t){e.push("[value cannot be serialized]")}}return e.join(n)}function j(t,n){return!!o(t)&&(r(n,"RegExp")?n.test(t):"string"==typeof n&&-1!==t.indexOf(n))}function k(t,n,e){if(!(n in t))return;const r=t[n],i=e(r);if("function"==typeof i)try{T(i,r)}catch(t){}t[n]=i}function O(t,n,e){Object.defineProperty(t,n,{value:e,writable:!0,configurable:!0})}function T(t,n){const e=n.prototype||{};t.prototype=n.prototype=e,O(t,"__sentry_original__",n)}function D(t){return t.__sentry_original__}function R(t){if(e(t))return{message:t.message,name:t.name,stack:t.stack,...M(t)};if(a(t)){const n={type:t.type,target:I(t.target),currentTarget:I(t.currentTarget),...M(t)};return"undefined"!=typeof CustomEvent&&h(t,CustomEvent)&&(n.detail=t.detail),n}return t}function I(t){try{return n=t,"undefined"!=typeof Element&&h(n,Element)?m(t):Object.prototype.toString.call(t)}catch(t){return"<unknown>"}var n}function M(t){if("object"==typeof t&&null!==t){const n={};for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&(n[e]=t[e]);return n}return{}}function C(t,n=40){const e=Object.keys(R(t));if(e.sort(),!e.length)return"[object has no keys]";if(e[0].length>=n)return $(e[0],n);for(let t=e.length;t>0;t--){const r=e.slice(0,t).join(", ");if(!(r.length>n))return t===e.length?r:$(r,n)}return""}function N(t){return A(t,new Map)}function A(t,n){if(u(t)){const e=n.get(t);if(void 0!==e)return e;const r={};n.set(t,r);for(const e of Object.keys(t))void 0!==t[e]&&(r[e]=A(t[e],n));return r}if(Array.isArray(t)){const e=n.get(t);if(void 0!==e)return e;const r=[];return n.set(t,r),t.forEach((t=>{r.push(A(t,n))})),r}return t}!function(){const t={enable:()=>{},disable:()=>{}};S.forEach((n=>{t[n]=()=>{}}))}();function L(...t){const n=t.sort(((t,n)=>t[0]-n[0])).map((t=>t[1]));return(t,e=0)=>{const r=[];for(const i of t.split("\n").slice(e)){const t=i.replace(/\(error: (.*)\)/,"$1");for(const e of n){const n=e(t);if(n){r.push(n);break}}}return function(t){if(!t.length)return[];let n=t;const e=n[0].function||"",r=n[n.length-1].function||"";-1===e.indexOf("captureMessage")&&-1===e.indexOf("captureException")||(n=n.slice(1));-1!==r.indexOf("sentryWrapped")&&(n=n.slice(0,-1));return n.slice(0,50).map((t=>({...t,filename:t.filename||n[0].filename,function:t.function||"?"}))).reverse()}(r)}}const U="<anonymous>";function q(t){try{return t&&"function"==typeof t&&t.name||U}catch(t){return U}}function P(){if(!("fetch"in y))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch(t){return!1}}function H(t){return t&&/^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}const F={},B={};function X(t){if(!B[t])switch(B[t]=!0,t){case"console":!function(){if(!("console"in y))return;S.forEach((function(t){t in y.console&&k(y.console,t,(function(n){return function(...e){W("console",{args:e,level:t}),n&&n.apply(y.console,e)}}))}))}();break;case"dom":!function(){if(!("document"in y))return;const t=W.bind(null,"dom"),n=Y(t,!0);y.document.addEventListener("click",n,!1),y.document.addEventListener("keypress",n,!1),["EventTarget","Node"].forEach((n=>{const e=y[n]&&y[n].prototype;e&&e.hasOwnProperty&&e.hasOwnProperty("addEventListener")&&(k(e,"addEventListener",(function(n){return function(e,r,i){if("click"===e||"keypress"==e)try{const r=this,s=r.__sentry_instrumentation_handlers__=r.__sentry_instrumentation_handlers__||{},o=s[e]=s[e]||{refCount:0};if(!o.handler){const r=Y(t);o.handler=r,n.call(this,e,r,i)}o.refCount+=1}catch(t){}return n.call(this,e,r,i)}})),k(e,"removeEventListener",(function(t){return function(n,e,r){if("click"===n||"keypress"==n)try{const e=this,i=e.__sentry_instrumentation_handlers__||{},s=i[n];s&&(s.refCount-=1,s.refCount<=0&&(t.call(this,n,s.handler,r),s.handler=void 0,delete i[n]),0===Object.keys(i).length&&delete e.__sentry_instrumentation_handlers__)}catch(t){}return t.call(this,n,e,r)}})))}))}();break;case"xhr":!function(){if(!("XMLHttpRequest"in y))return;const t=XMLHttpRequest.prototype;k(t,"open",(function(t){return function(...n){const e=this,r=n[1],i=e.__sentry_xhr__={method:o(n[0])?n[0].toUpperCase():n[0],url:n[1]};o(r)&&"POST"===i.method&&r.match(/sentry_key/)&&(e.__sentry_own_request__=!0);const s=function(){if(4===e.readyState){try{i.status_code=e.status}catch(t){}W("xhr",{args:n,endTimestamp:Date.now(),startTimestamp:Date.now(),xhr:e})}};return"onreadystatechange"in e&&"function"==typeof e.onreadystatechange?k(e,"onreadystatechange",(function(t){return function(...n){return s(),t.apply(e,n)}})):e.addEventListener("readystatechange",s),t.apply(e,n)}})),k(t,"send",(function(t){return function(...n){return this.__sentry_xhr__&&void 0!==n[0]&&(this.__sentry_xhr__.body=n[0]),W("xhr",{args:n,startTimestamp:Date.now(),xhr:this}),t.apply(this,n)}}))}();break;case"fetch":!function(){if(!function(){if(!P())return!1;if(H(y.fetch))return!0;let t=!1;const n=y.document;if(n&&"function"==typeof n.createElement)try{const e=n.createElement("iframe");e.hidden=!0,n.head.appendChild(e),e.contentWindow&&e.contentWindow.fetch&&(t=H(e.contentWindow.fetch)),n.head.removeChild(e)}catch(t){}return t}())return;k(y,"fetch",(function(t){return function(...n){const e={args:n,fetchData:{method:J(n),url:K(n)},startTimestamp:Date.now()};return W("fetch",{...e}),t.apply(y,n).then((t=>(W("fetch",{...e,endTimestamp:Date.now(),response:t}),t)),(t=>{throw W("fetch",{...e,endTimestamp:Date.now(),error:t}),t}))}}))}();break;case"history":!function(){if(!function(){const t=y.chrome,n=t&&t.app&&t.app.runtime,e="history"in y&&!!y.history.pushState&&!!y.history.replaceState;return!n&&e}())return;const t=y.onpopstate;function n(t){return function(...n){const e=n.length>2?n[2]:void 0;if(e){const t=G,n=String(e);G=n,W("history",{from:t,to:n})}return t.apply(this,n)}}y.onpopstate=function(...n){const e=y.location.href,r=G;if(G=e,W("history",{from:r,to:e}),t)try{return t.apply(this,n)}catch(t){}},k(y.history,"pushState",n),k(y.history,"replaceState",n)}();break;case"error":Z=y.onerror,y.onerror=function(t,n,e,r,i){return W("error",{column:r,error:i,line:e,msg:t,url:n}),!!Z&&Z.apply(this,arguments)};break;case"unhandledrejection":tt=y.onunhandledrejection,y.onunhandledrejection=function(t){return W("unhandledrejection",t),!tt||tt.apply(this,arguments)};break;default:return}}function z(t,n){F[t]=F[t]||[],F[t].push(n),X(t)}function W(t,n){if(t&&F[t])for(const e of F[t]||[])try{e(n)}catch(t){}}function J(t=[]){return"Request"in y&&h(t[0],Request)&&t[0].method?String(t[0].method).toUpperCase():t[1]&&t[1].method?String(t[1].method).toUpperCase():"GET"}function K(t=[]){return"string"==typeof t[0]?t[0]:"Request"in y&&h(t[0],Request)?t[0].url:String(t[0])}let G;let V,Q;function Y(t,n=!1){return e=>{if(!e||Q===e)return;if(function(t){if("keypress"!==t.type)return!1;try{const n=t.target;if(!n||!n.tagName)return!0;if("INPUT"===n.tagName||"TEXTAREA"===n.tagName||n.isContentEditable)return!1}catch(t){}return!0}(e))return;const r="keypress"===e.type?"input":e.type;(void 0===V||function(t,n){if(!t)return!0;if(t.type!==n.type)return!0;try{if(t.target!==n.target)return!0}catch(t){}return!1}(Q,e))&&(t({event:e,name:r,global:n}),Q=e),clearTimeout(V),V=y.setTimeout((()=>{V=void 0}),1e3)}}let Z=null;let tt=null;function nt(){const t=d,n=t.crypto||t.msCrypto;if(n&&n.randomUUID)return n.randomUUID().replace(/-/g,"");const e=n&&n.getRandomValues?()=>n.getRandomValues(new Uint8Array(1))[0]:()=>16*Math.random();return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,(t=>(t^(15&e())>>t/4).toString(16)))}function et(t){return t.exception&&t.exception.values?t.exception.values[0]:void 0}function rt(t){const{message:n,event_id:e}=t;if(n)return n;const r=et(t);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||e||"<unknown>":e||"<unknown>"}function it(t,n,e){const r=t.exception=t.exception||{},i=r.values=r.values||[],s=i[0]=i[0]||{};s.value||(s.value=n||""),s.type||(s.type=e||"Error")}function st(t,n){const e=et(t);if(!e)return;const r=e.mechanism;if(e.mechanism={type:"generic",handled:!0,...r,...n},n&&"data"in n){const t={...r&&r.data,...n.data};e.mechanism.data=t}}function ot(t){if(t&&t.__sentry_captured__)return!0;try{O(t,"__sentry_captured__",!0)}catch(t){}return!1}function ct(t){return Array.isArray(t)?t:[t]}function ut(t,n=1/0,e=1/0){try{return ft("",t,n,e)}catch(t){return{ERROR:`**non-serializable** (${t})`}}}function at(t,n=3,e=102400){const r=ut(t,n);return i=r,function(t){return~-encodeURI(t).split(/%..|./).length}(JSON.stringify(i))>e?at(t,n-1,e):r;var i}function ft(t,n,e=1/0,r=1/0,i=function(){const t="function"==typeof WeakSet,n=t?new WeakSet:[];return[function(e){if(t)return!!n.has(e)||(n.add(e),!1);for(let t=0;t<n.length;t++)if(n[t]===e)return!0;return n.push(e),!1},function(e){if(t)n.delete(e);else for(let t=0;t<n.length;t++)if(n[t]===e){n.splice(t,1);break}}]}()){const[s,o]=i;if(null===n||["number","boolean","string"].includes(typeof n)&&("number"!=typeof(c=n)||c==c))return n;var c;const a=function(t,n){try{return"domain"===t&&n&&"object"==typeof n&&n.t?"[Domain]":"domainEmitter"===t?"[DomainEmitter]":"undefined"!=typeof global&&n===global?"[Global]":"undefined"!=typeof window&&n===window?"[Window]":"undefined"!=typeof document&&n===document?"[Document]":function(t){return u(t)&&"nativeEvent"in t&&"preventDefault"in t&&"stopPropagation"in t}(n)?"[SyntheticEvent]":"number"==typeof n&&n!=n?"[NaN]":void 0===n?"[undefined]":"function"==typeof n?`[Function: ${q(n)}]`:"symbol"==typeof n?`[${String(n)}]`:"bigint"==typeof n?`[BigInt: ${String(n)}]`:`[object ${Object.getPrototypeOf(n).constructor.name}]`}catch(t){return`**non-serializable** (${t})`}}(t,n);if(!a.startsWith("[object "))return a;if(n.__sentry_skip_normalization__)return n;if(0===e)return a.replace("object ","");if(s(n))return"[Circular ~]";const f=n;if(f&&"function"==typeof f.toJSON)try{return ft("",f.toJSON(),e-1,r,i)}catch(t){}const h=Array.isArray(n)?[]:{};let l=0;const d=R(n);for(const t in d){if(!Object.prototype.hasOwnProperty.call(d,t))continue;if(l>=r){h[t]="[MaxProperties ~]";break}const n=d[t];h[t]=ft(t,n,e-1,r,i),l+=1}return o(n),h}var ht;function lt(t){return new pt((n=>{n(t)}))}function dt(t){return new pt(((n,e)=>{e(t)}))}!function(t){t[t.PENDING=0]="PENDING";t[t.RESOLVED=1]="RESOLVED";t[t.REJECTED=2]="REJECTED"}(ht||(ht={}));class pt{__init(){this.i=ht.PENDING}__init2(){this.o=[]}constructor(t){pt.prototype.__init.call(this),pt.prototype.__init2.call(this),pt.prototype.__init3.call(this),pt.prototype.__init4.call(this),pt.prototype.__init5.call(this),pt.prototype.__init6.call(this);try{t(this.u,this.h)}catch(t){this.h(t)}}then(t,n){return new pt(((e,r)=>{this.o.push([!1,n=>{if(t)try{e(t(n))}catch(t){r(t)}else e(n)},t=>{if(n)try{e(n(t))}catch(t){r(t)}else r(t)}]),this.l()}))}catch(t){return this.then((t=>t),t)}finally(t){return new pt(((n,e)=>{let r,i;return this.then((n=>{i=!1,r=n,t&&t()}),(n=>{i=!0,r=n,t&&t()})).then((()=>{i?e(r):n(r)}))}))}__init3(){this.u=t=>{this.p(ht.RESOLVED,t)}}__init4(){this.h=t=>{this.p(ht.REJECTED,t)}}__init5(){this.p=(t,n)=>{this.i===ht.PENDING&&(f(n)?n.then(this.u,this.h):(this.i=t,this.m=n,this.l()))}}__init6(){this.l=()=>{if(this.i===ht.PENDING)return;const t=this.o.slice();this.o=[],t.forEach((t=>{t[0]||(this.i===ht.RESOLVED&&t[1](this.m),this.i===ht.REJECTED&&t[2](this.m),t[0]=!0)}))}}}function yt(t){const n=[];function e(t){return n.splice(n.indexOf(t),1)[0]}return{$:n,add:function(r){if(!(void 0===t||n.length<t))return dt(new g("Not adding Promise because buffer limit was reached."));const i=r();return-1===n.indexOf(i)&&n.push(i),i.then((()=>e(i))).then(null,(()=>e(i).then(null,(()=>{})))),i},drain:function(t){return new pt(((e,r)=>{let i=n.length;if(!i)return e(!0);const s=setTimeout((()=>{t&&t>0&&e(!1)}),t);n.forEach((t=>{lt(t).then((()=>{--i||(clearTimeout(s),e(!0))}),r)}))}))}}}function mt(t){if(!t)return{};const n=t.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!n)return{};const e=n[6]||"",r=n[8]||"";return{host:n[4],path:n[5],protocol:n[2],relative:n[5]+e+r}}const vt=["fatal","error","warning","log","info","debug"];const gt={nowSeconds:()=>Date.now()/1e3};const bt=function(){const{performance:t}=y;if(!t||!t.now)return;return{now:()=>t.now(),timeOrigin:Date.now()-t.now()}}(),_t=void 0===bt?gt:{nowSeconds:()=>(bt.timeOrigin+bt.now())/1e3},wt=gt.nowSeconds.bind(gt),Et=_t.nowSeconds.bind(_t);function St(t,n=[]){return[t,n]}function $t(t,n){const[e,r]=t;return[e,[...r,n]]}function xt(t,n){t[1].forEach((t=>{const e=t[0].type;n(t,e)}))}function jt(t,n){return(n||new TextEncoder).encode(t)}function kt(t,n){const[e,r]=t;let i=JSON.stringify(e);function s(t){"string"==typeof i?i="string"==typeof t?i+t:[jt(i,n),t]:i.push("string"==typeof t?jt(t,n):t)}for(const t of r){const[n,e]=t;if(s(`\n${JSON.stringify(n)}\n`),"string"==typeof e||e instanceof Uint8Array)s(e);else{let t;try{t=JSON.stringify(e)}catch(n){t=JSON.stringify(ut(e))}s(t)}}return"string"==typeof i?i:function(t){const n=t.reduce(((t,n)=>t+n.length),0),e=new Uint8Array(n);let r=0;for(const n of t)e.set(n,r),r+=n.length;return e}(i)}function Ot(t,n){const e="string"==typeof t.data?jt(t.data,n):t.data;return[N({type:"attachment",length:e.length,filename:t.filename,content_type:t.contentType,attachment_type:t.attachmentType}),e]}(()=>{const{performance:t}=y;if(!t||!t.now)return;const n=36e5,e=t.now(),r=Date.now(),i=t.timeOrigin?Math.abs(t.timeOrigin+e-r):n,s=i<n,o=t.timing&&t.timing.navigationStart,c="number"==typeof o?Math.abs(o+e-r):n;(s||c<n)&&(i<=c&&t.timeOrigin)})();const Tt={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default"};function Dt(t){return Tt[t]}function Rt(t,{statusCode:n,headers:e},r=Date.now()){const i={...t},s=e&&e["x-sentry-rate-limits"],o=e&&e["retry-after"];if(s)for(const t of s.trim().split(",")){const[n,e]=t.split(":",2),s=parseInt(n,10),o=1e3*(isNaN(s)?60:s);if(e)for(const t of e.split(";"))i[t]=r+o;else i.all=r+o}else o?i.all=r+function(t,n=Date.now()){const e=parseInt(`${t}`,10);if(!isNaN(e))return 1e3*e;const r=Date.parse(`${t}`);return isNaN(r)?6e4:r-n}(o,r):429===n&&(i.all=r+6e4);return i}function It(t){const n=Et(),e={sid:nt(),init:!0,timestamp:n,started:n,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>function(t){return N({sid:`${t.sid}`,init:t.init,started:new Date(1e3*t.started).toISOString(),timestamp:new Date(1e3*t.timestamp).toISOString(),status:t.status,errors:t.errors,did:"number"==typeof t.did||"string"==typeof t.did?`${t.did}`:void 0,duration:t.duration,attrs:{release:t.release,environment:t.environment,ip_address:t.ipAddress,user_agent:t.userAgent}})}(e)};return t&&Mt(e,t),e}function Mt(t,n={}){if(n.user&&(!t.ipAddress&&n.user.ip_address&&(t.ipAddress=n.user.ip_address),t.did||n.did||(t.did=n.user.id||n.user.email||n.user.username)),t.timestamp=n.timestamp||Et(),n.ignoreDuration&&(t.ignoreDuration=n.ignoreDuration),n.sid&&(t.sid=32===n.sid.length?n.sid:nt()),void 0!==n.init&&(t.init=n.init),!t.did&&n.did&&(t.did=`${n.did}`),"number"==typeof n.started&&(t.started=n.started),t.ignoreDuration)t.duration=void 0;else if("number"==typeof n.duration)t.duration=n.duration;else{const n=t.timestamp-t.started;t.duration=n>=0?n:0}n.release&&(t.release=n.release),n.environment&&(t.environment=n.environment),!t.ipAddress&&n.ipAddress&&(t.ipAddress=n.ipAddress),!t.userAgent&&n.userAgent&&(t.userAgent=n.userAgent),"number"==typeof n.errors&&(t.errors=n.errors),n.status&&(t.status=n.status)}class Ct{constructor(){this.v=!1,this.g=[],this._=[],this.S=[],this.j=[],this.k={},this.O={},this.T={},this.D={},this.R={}}static clone(t){const n=new Ct;return t&&(n.S=[...t.S],n.O={...t.O},n.T={...t.T},n.D={...t.D},n.k=t.k,n.I=t.I,n.M=t.M,n.C=t.C,n.N=t.N,n.A=t.A,n._=[...t._],n.L=t.L,n.j=[...t.j]),n}addScopeListener(t){this.g.push(t)}addEventProcessor(t){return this._.push(t),this}setUser(t){return this.k=t||{},this.C&&Mt(this.C,{user:t}),this.U(),this}getUser(){return this.k}getRequestSession(){return this.L}setRequestSession(t){return this.L=t,this}setTags(t){return this.O={...this.O,...t},this.U(),this}setTag(t,n){return this.O={...this.O,[t]:n},this.U(),this}setExtras(t){return this.T={...this.T,...t},this.U(),this}setExtra(t,n){return this.T={...this.T,[t]:n},this.U(),this}setFingerprint(t){return this.A=t,this.U(),this}setLevel(t){return this.I=t,this.U(),this}setTransactionName(t){return this.N=t,this.U(),this}setContext(t,n){return null===n?delete this.D[t]:this.D={...this.D,[t]:n},this.U(),this}setSpan(t){return this.M=t,this.U(),this}getSpan(){return this.M}getTransaction(){const t=this.getSpan();return t&&t.transaction}setSession(t){return t?this.C=t:delete this.C,this.U(),this}getSession(){return this.C}update(t){if(!t)return this;if("function"==typeof t){const n=t(this);return n instanceof Ct?n:this}return t instanceof Ct?(this.O={...this.O,...t.O},this.T={...this.T,...t.T},this.D={...this.D,...t.D},t.k&&Object.keys(t.k).length&&(this.k=t.k),t.I&&(this.I=t.I),t.A&&(this.A=t.A),t.L&&(this.L=t.L)):u(t)&&(t=t,this.O={...this.O,...t.tags},this.T={...this.T,...t.extra},this.D={...this.D,...t.contexts},t.user&&(this.k=t.user),t.level&&(this.I=t.level),t.fingerprint&&(this.A=t.fingerprint),t.requestSession&&(this.L=t.requestSession)),this}clear(){return this.S=[],this.O={},this.T={},this.k={},this.D={},this.I=void 0,this.N=void 0,this.A=void 0,this.L=void 0,this.M=void 0,this.C=void 0,this.U(),this.j=[],this}addBreadcrumb(t,n){const e="number"==typeof n?n:100;if(e<=0)return this;const r={timestamp:wt(),...t};return this.S=[...this.S,r].slice(-e),this.U(),this}clearBreadcrumbs(){return this.S=[],this.U(),this}addAttachment(t){return this.j.push(t),this}getAttachments(){return this.j}clearAttachments(){return this.j=[],this}applyToEvent(t,n={}){if(this.T&&Object.keys(this.T).length&&(t.extra={...this.T,...t.extra}),this.O&&Object.keys(this.O).length&&(t.tags={...this.O,...t.tags}),this.k&&Object.keys(this.k).length&&(t.user={...this.k,...t.user}),this.D&&Object.keys(this.D).length&&(t.contexts={...this.D,...t.contexts}),this.I&&(t.level=this.I),this.N&&(t.transaction=this.N),this.M){t.contexts={trace:this.M.getTraceContext(),...t.contexts};const n=this.M.transaction&&this.M.transaction.name;n&&(t.tags={transaction:n,...t.tags})}return this.q(t),t.breadcrumbs=[...t.breadcrumbs||[],...this.S],t.breadcrumbs=t.breadcrumbs.length>0?t.breadcrumbs:void 0,t.sdkProcessingMetadata={...t.sdkProcessingMetadata,...this.R},this.P([...Nt(),...this._],t,n)}setSDKProcessingMetadata(t){return this.R={...this.R,...t},this}P(t,n,e,r=0){return new pt(((i,s)=>{const o=t[r];if(null===n||"function"!=typeof o)i(n);else{const c=o({...n},e);f(c)?c.then((n=>this.P(t,n,e,r+1).then(i))).then(null,s):this.P(t,c,e,r+1).then(i).then(null,s)}}))}U(){this.v||(this.v=!0,this.g.forEach((t=>{t(this)})),this.v=!1)}q(t){t.fingerprint=t.fingerprint?ct(t.fingerprint):[],this.A&&(t.fingerprint=t.fingerprint.concat(this.A)),t.fingerprint&&!t.fingerprint.length&&delete t.fingerprint}}function Nt(){return p("globalEventProcessors",(()=>[]))}function At(t){Nt().push(t)}const Lt=100;class Ut{__init(){this.H=[{}]}constructor(t,n=new Ct,e=4){this.F=e,Ut.prototype.__init.call(this),this.getStackTop().scope=n,t&&this.bindClient(t)}isOlderThan(t){return this.F<t}bindClient(t){this.getStackTop().client=t,t&&t.setupIntegrations&&t.setupIntegrations()}pushScope(){const t=Ct.clone(this.getScope());return this.getStack().push({client:this.getClient(),scope:t}),t}popScope(){return!(this.getStack().length<=1)&&!!this.getStack().pop()}withScope(t){const n=this.pushScope();try{t(n)}finally{this.popScope()}}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getStack(){return this.H}getStackTop(){return this.H[this.H.length-1]}captureException(t,n){const e=this.B=n&&n.event_id?n.event_id:nt(),r=new Error("Sentry syntheticException");return this.X(((i,s)=>{i.captureException(t,{originalException:t,syntheticException:r,...n,event_id:e},s)})),e}captureMessage(t,n,e){const r=this.B=e&&e.event_id?e.event_id:nt(),i=new Error(t);return this.X(((s,o)=>{s.captureMessage(t,n,{originalException:t,syntheticException:i,...e,event_id:r},o)})),r}captureEvent(t,n){const e=n&&n.event_id?n.event_id:nt();return"transaction"!==t.type&&(this.B=e),this.X(((r,i)=>{r.captureEvent(t,{...n,event_id:e},i)})),e}lastEventId(){return this.B}addBreadcrumb(t,n){const{scope:e,client:r}=this.getStackTop();if(!e||!r)return;const{beforeBreadcrumb:i=null,maxBreadcrumbs:s=Lt}=r.getOptions&&r.getOptions()||{};if(s<=0)return;const o={timestamp:wt(),...t},c=i?function(t){if(!("console"in d))return t();const n=d.console,e={};S.forEach((t=>{const r=n[t]&&n[t].__sentry_original__;t in n&&r&&(e[t]=n[t],n[t]=r)}));try{return t()}finally{Object.keys(e).forEach((t=>{n[t]=e[t]}))}}((()=>i(o,n))):o;null!==c&&e.addBreadcrumb(c,s)}setUser(t){const n=this.getScope();n&&n.setUser(t)}setTags(t){const n=this.getScope();n&&n.setTags(t)}setExtras(t){const n=this.getScope();n&&n.setExtras(t)}setTag(t,n){const e=this.getScope();e&&e.setTag(t,n)}setExtra(t,n){const e=this.getScope();e&&e.setExtra(t,n)}setContext(t,n){const e=this.getScope();e&&e.setContext(t,n)}configureScope(t){const{scope:n,client:e}=this.getStackTop();n&&e&&t(n)}run(t){const n=Pt(this);try{t(this)}finally{Pt(n)}}getIntegration(t){const n=this.getClient();if(!n)return null;try{return n.getIntegration(t)}catch(t){return null}}startTransaction(t,n){return this.W("startTransaction",t,n)}traceHeaders(){return this.W("traceHeaders")}captureSession(t=!1){if(t)return this.endSession();this.J()}endSession(){const t=this.getStackTop(),n=t&&t.scope,e=n&&n.getSession();e&&function(t,n){let e={};n?e={status:n}:"ok"===t.status&&(e={status:"exited"}),Mt(t,e)}(e),this.J(),n&&n.setSession()}startSession(t){const{scope:n,client:e}=this.getStackTop(),{release:r,environment:i}=e&&e.getOptions()||{},{userAgent:s}=d.navigator||{},o=It({release:r,environment:i,...n&&{user:n.getUser()},...s&&{userAgent:s},...t});if(n){const t=n.getSession&&n.getSession();t&&"ok"===t.status&&Mt(t,{status:"exited"}),this.endSession(),n.setSession(o)}return o}shouldSendDefaultPii(){const t=this.getClient(),n=t&&t.getOptions();return Boolean(n&&n.sendDefaultPii)}J(){const{scope:t,client:n}=this.getStackTop();if(!t)return;const e=t.getSession();e&&n&&n.captureSession&&n.captureSession(e)}X(t){const{scope:n,client:e}=this.getStackTop();e&&t(e,n)}W(t,...n){const e=qt().__SENTRY__;if(e&&e.extensions&&"function"==typeof e.extensions[t])return e.extensions[t].apply(this,n)}}function qt(){return d.__SENTRY__=d.__SENTRY__||{extensions:{},hub:void 0},d}function Pt(t){const n=qt(),e=Ft(n);return Bt(n,t),e}function Ht(){const t=qt();var n;return(n=t)&&n.__SENTRY__&&n.__SENTRY__.hub&&!Ft(t).isOlderThan(4)||Bt(t,new Ut),Ft(t)}function Ft(t){return p("hub",(()=>new Ut),t)}function Bt(t,n){if(!t)return!1;return(t.__SENTRY__=t.__SENTRY__||{}).hub=n,!0}function captureException(t,n){return Ht().captureException(t,{captureContext:n})}function Xt(t){Ht().withScope(t)}function zt(t){const n=t.protocol?`${t.protocol}:`:"",e=t.port?`:${t.port}`:"";return`${n}//${t.host}${e}${t.path?`/${t.path}`:""}/api/`}function Wt(t,n){return e={sentry_key:t.publicKey,sentry_version:"7",...n&&{sentry_client:`${n.name}/${n.version}`}},Object.keys(e).map((t=>`${encodeURIComponent(t)}=${encodeURIComponent(e[t])}`)).join("&");var e}function Jt(t,n={}){const e="string"==typeof n?n:n.tunnel,r="string"!=typeof n&&n.K?n.K.sdk:void 0;return e||`${function(t){return`${zt(t)}${t.projectId}/envelope/`}(t)}?${Wt(t,r)}`}function Kt(t){if(!t||!t.sdk)return;const{name:n,version:e}=t.sdk;return{name:n,version:e}}function Gt(t,n,e,r){const i=Kt(e),s=t.type||"event";!function(t,n){n&&(t.sdk=t.sdk||{},t.sdk.name=t.sdk.name||n.name,t.sdk.version=t.sdk.version||n.version,t.sdk.integrations=[...t.sdk.integrations||[],...n.integrations||[]],t.sdk.packages=[...t.sdk.packages||[],...n.packages||[]])}(t,e&&e.sdk);const o=function(t,n,e,r){const i=t.sdkProcessingMetadata&&t.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:t.event_id,sent_at:(new Date).toISOString(),...n&&{sdk:n},...!!e&&{dsn:_(r)},..."transaction"===t.type&&i&&{trace:N({...i})}}}(t,i,r,n);delete t.sdkProcessingMetadata;return St(o,[[{type:s},t]])}const Vt=[];function Qt(t){const n=t.defaultIntegrations||[],e=t.integrations;let r;n.forEach((t=>{t.isDefaultInstance=!0})),r=Array.isArray(e)?[...n,...e]:"function"==typeof e?ct(e(n)):n;const i=function(t){const n={};return t.forEach((t=>{const{name:e}=t,r=n[e];r&&!r.isDefaultInstance&&t.isDefaultInstance||(n[e]=t)})),Object.values(n)}(r),s=i.findIndex((t=>"Debug"===t.name));if(-1!==s){const[t]=i.splice(s,1);i.push(t)}return i}class Yt{__init(){this.G={}}__init2(){this.V=!1}__init3(){this.Y=0}__init4(){this.Z={}}constructor(t){if(Yt.prototype.__init.call(this),Yt.prototype.__init2.call(this),Yt.prototype.__init3.call(this),Yt.prototype.__init4.call(this),this.tt=t,t.dsn){this.nt=E(t.dsn);const n=Jt(this.nt,t);this.et=t.transport({recordDroppedEvent:this.recordDroppedEvent.bind(this),...t.transportOptions,url:n})}}captureException(t,n,e){if(ot(t))return;let r=n&&n.event_id;return this.rt(this.eventFromException(t,n).then((t=>this.it(t,n,e))).then((t=>{r=t}))),r}captureMessage(t,n,e,r){let i=e&&e.event_id;const s=c(t)?this.eventFromMessage(String(t),n,e):this.eventFromException(t,e);return this.rt(s.then((t=>this.it(t,e,r))).then((t=>{i=t}))),i}captureEvent(t,n,e){if(n&&n.originalException&&ot(n.originalException))return;let r=n&&n.event_id;return this.rt(this.it(t,n,e).then((t=>{r=t}))),r}captureSession(t){this.st()&&("string"!=typeof t.release||(this.sendSession(t),Mt(t,{init:!1})))}getDsn(){return this.nt}getOptions(){return this.tt}getTransport(){return this.et}flush(t){const n=this.et;return n?this.ot(t).then((e=>n.flush(t).then((t=>e&&t)))):lt(!0)}close(t){return this.flush(t).then((t=>(this.getOptions().enabled=!1,t)))}setupIntegrations(){this.st()&&!this.V&&(this.G=function(t){const n={};return t.forEach((t=>{n[t.name]=t,-1===Vt.indexOf(t.name)&&(t.setupOnce(At,Ht),Vt.push(t.name))})),n}(this.tt.integrations),this.V=!0)}getIntegrationById(t){return this.G[t]}getIntegration(t){try{return this.G[t.id]||null}catch(t){return null}}sendEvent(t,n={}){if(this.nt){let e=Gt(t,this.nt,this.tt.K,this.tt.tunnel);for(const t of n.attachments||[])e=$t(e,Ot(t,this.tt.transportOptions&&this.tt.transportOptions.textEncoder));this.ct(e)}}sendSession(t){if(this.nt){const n=function(t,n,e,r){const i=Kt(e);return St({sent_at:(new Date).toISOString(),...i&&{sdk:i},...!!r&&{dsn:_(n)}},["aggregates"in t?[{type:"sessions"},t]:[{type:"session"},t]])}(t,this.nt,this.tt.K,this.tt.tunnel);this.ct(n)}}recordDroppedEvent(t,n){if(this.tt.sendClientReports){const e=`${t}:${n}`;this.Z[e]=this.Z[e]+1||1}}ut(t,n){let e=!1,r=!1;const i=n.exception&&n.exception.values;if(i){r=!0;for(const t of i){const n=t.mechanism;if(n&&!1===n.handled){e=!0;break}}}const s="ok"===t.status;(s&&0===t.errors||s&&e)&&(Mt(t,{...e&&{status:"crashed"},errors:t.errors||Number(r||e)}),this.captureSession(t))}ot(t){return new pt((n=>{let e=0;const r=setInterval((()=>{0==this.Y?(clearInterval(r),n(!0)):(e+=1,t&&e>=t&&(clearInterval(r),n(!1)))}),1)}))}st(){return!1!==this.getOptions().enabled&&void 0!==this.nt}at(t,n,e){const{normalizeDepth:r=3,normalizeMaxBreadth:i=1e3}=this.getOptions(),s={...t,event_id:t.event_id||n.event_id||nt(),timestamp:t.timestamp||wt()};this.ft(s),this.ht(s);let o=e;n.captureContext&&(o=Ct.clone(o).update(n.captureContext));let c=lt(s);if(o){const t=[...n.attachments||[],...o.getAttachments()];t.length&&(n.attachments=t),c=o.applyToEvent(s,n)}return c.then((t=>"number"==typeof r&&r>0?this.lt(t,r,i):t))}lt(t,n,e){if(!t)return null;const r={...t,...t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map((t=>({...t,...t.data&&{data:ut(t.data,n,e)}})))},...t.user&&{user:ut(t.user,n,e)},...t.contexts&&{contexts:ut(t.contexts,n,e)},...t.extra&&{extra:ut(t.extra,n,e)}};return t.contexts&&t.contexts.trace&&r.contexts&&(r.contexts.trace=t.contexts.trace,t.contexts.trace.data&&(r.contexts.trace.data=ut(t.contexts.trace.data,n,e))),t.spans&&(r.spans=t.spans.map((t=>(t.data&&(t.data=ut(t.data,n,e)),t)))),r}ft(t){const n=this.getOptions(),{environment:e,release:r,dist:i,maxValueLength:s=250}=n;"environment"in t||(t.environment="environment"in n?e:"production"),void 0===t.release&&void 0!==r&&(t.release=r),void 0===t.dist&&void 0!==i&&(t.dist=i),t.message&&(t.message=$(t.message,s));const o=t.exception&&t.exception.values&&t.exception.values[0];o&&o.value&&(o.value=$(o.value,s));const c=t.request;c&&c.url&&(c.url=$(c.url,s))}ht(t){const n=Object.keys(this.G);n.length>0&&(t.sdk=t.sdk||{},t.sdk.integrations=[...t.sdk.integrations||[],...n])}it(t,n={},e){return this.dt(t,n,e).then((t=>t.event_id),(t=>{}))}dt(t,n,e){const{beforeSend:r,sampleRate:i}=this.getOptions();if(!this.st())return dt(new g("SDK not enabled, will not capture event.","log"));const s="transaction"===t.type;return!s&&"number"==typeof i&&Math.random()>i?(this.recordDroppedEvent("sample_rate","error"),dt(new g(`Discarding event because it's not included in the random sample (sampling rate = ${i})`,"log"))):this.at(t,n,e).then((e=>{if(null===e)throw this.recordDroppedEvent("event_processor",t.type||"error"),new g("An event processor returned null, will not send event.","log");if(n.data&&!0===n.data.__sentry__||s||!r)return e;return function(t){const n="`beforeSend` method has to return `null` or a valid event.";if(f(t))return t.then((t=>{if(!u(t)&&null!==t)throw new g(n);return t}),(t=>{throw new g(`beforeSend rejected with ${t}`)}));if(!u(t)&&null!==t)throw new g(n);return t}(r(e,n))})).then((r=>{if(null===r)throw this.recordDroppedEvent("before_send",t.type||"error"),new g("`beforeSend` returned `null`, will not send event.","log");const i=e&&e.getSession();!s&&i&&this.ut(i,r);const o=r.transaction_info;if(s&&o&&r.transaction!==t.transaction){const t="custom";r.transaction_info={...o,source:t,changes:[...o.changes,{source:t,timestamp:r.timestamp,propagations:o.propagations}]}}return this.sendEvent(r,n),r})).then(null,(t=>{if(t instanceof g)throw t;throw this.captureException(t,{data:{__sentry__:!0},originalException:t}),new g(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\nReason: ${t}`)}))}rt(t){this.Y+=1,t.then((t=>(this.Y-=1,t)),(t=>(this.Y-=1,t)))}ct(t){this.et&&this.nt&&this.et.send(t).then(null,(t=>{}))}yt(){const t=this.Z;return this.Z={},Object.keys(t).map((n=>{const[e,r]=n.split(":");return{reason:e,category:r,quantity:t[n]}}))}}function Zt(t,n,e=yt(t.bufferSize||30)){let r={};return{send:function(i){const s=[];if(xt(i,((n,e)=>{const i=Dt(e);!function(t,n,e=Date.now()){return function(t,n){return t[n]||t.all||0}(t,n)>e}(r,i)?s.push(n):t.recordDroppedEvent("ratelimit_backoff",i)})),0===s.length)return lt();const o=St(i[0],s),c=n=>{xt(o,((e,r)=>{t.recordDroppedEvent(n,Dt(r))}))};return e.add((()=>n({body:kt(o,t.textEncoder)}).then((t=>{r=Rt(r,t)}),(t=>{c("network_error")})))).then((t=>t),(t=>{if(t instanceof g)return c("queue_overflow"),lt();throw t}))},flush:t=>e.drain(t)}}const tn="7.16.0";let nn;class en{constructor(){en.prototype.__init.call(this)}static __initStatic(){this.id="FunctionToString"}__init(){this.name=en.id}setupOnce(){nn=Function.prototype.toString,Function.prototype.toString=function(...t){const n=D(this)||this;return nn.apply(n,t)}}}en.__initStatic();const rn=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/];class sn{static __initStatic(){this.id="InboundFilters"}__init(){this.name=sn.id}constructor(t={}){this.tt=t,sn.prototype.__init.call(this)}setupOnce(t,n){const e=t=>{const e=n();if(e){const n=e.getIntegration(sn);if(n){const r=e.getClient(),i=r?r.getOptions():{},s=function(t={},n={}){return{allowUrls:[...t.allowUrls||[],...n.allowUrls||[]],denyUrls:[...t.denyUrls||[],...n.denyUrls||[]],ignoreErrors:[...t.ignoreErrors||[],...n.ignoreErrors||[],...rn],ignoreInternal:void 0===t.ignoreInternal||t.ignoreInternal}}(n.tt,i);return function(t,n){if(n.ignoreInternal&&function(t){try{return"SentryError"===t.exception.values[0].type}catch(t){}return!1}(t))return!0;if(function(t,n){if(!n||!n.length)return!1;return function(t){if(t.message)return[t.message];if(t.exception)try{const{type:n="",value:e=""}=t.exception.values&&t.exception.values[0]||{};return[`${e}`,`${n}: ${e}`]}catch(t){return[]}return[]}(t).some((t=>n.some((n=>j(t,n)))))}(t,n.ignoreErrors))return!0;if(function(t,n){if(!n||!n.length)return!1;const e=on(t);return!!e&&n.some((t=>j(e,t)))}(t,n.denyUrls))return!0;if(!function(t,n){if(!n||!n.length)return!0;const e=on(t);return!e||n.some((t=>j(e,t)))}(t,n.allowUrls))return!0;return!1}(t,s)?null:t}}return t};e.id=this.name,t(e)}}function on(t){try{let n;try{n=t.exception.values[0].stacktrace.frames}catch(t){}return n?function(t=[]){for(let n=t.length-1;n>=0;n--){const e=t[n];if(e&&"<anonymous>"!==e.filename&&"[native code]"!==e.filename)return e.filename||null}return null}(n):null}catch(t){return null}}sn.__initStatic();var cn=Object.freeze({__proto__:null,FunctionToString:en,InboundFilters:sn});function un(t,n){const e=fn(t,n),r={type:n&&n.name,value:ln(n)};return e.length&&(r.stacktrace={frames:e}),void 0===r.type&&""===r.value&&(r.value="Unrecoverable error caught"),r}function an(t,n){return{exception:{values:[un(t,n)]}}}function fn(t,n){const e=n.stacktrace||n.stack||"",r=function(t){if(t){if("number"==typeof t.framesToPop)return t.framesToPop;if(hn.test(t.message))return 1}return 0}(n);try{return t(e,r)}catch(t){}return[]}const hn=/Minified React error #\d+;/i;function ln(t){const n=t&&t.message;return n?n.error&&"string"==typeof n.error.message?n.error.message:n:"No error message"}function dn(t,n,o,c,f){let h;if(i(n)&&n.error){return an(t,n.error)}if(s(n)||r(n,"DOMException")){const e=n;if("stack"in n)h=an(t,n);else{const n=e.name||(s(e)?"DOMError":"DOMException"),r=e.message?`${n}: ${e.message}`:n;h=pn(t,r,o,c),it(h,r)}return"code"in e&&(h.tags={...h.tags,"DOMException.code":`${e.code}`}),h}if(e(n))return an(t,n);if(u(n)||a(n)){return h=function(t,n,e,r){const i=Ht().getClient(),s=i&&i.getOptions().normalizeDepth,o={exception:{values:[{type:a(n)?n.constructor.name:r?"UnhandledRejection":"Error",value:`Non-Error ${r?"promise rejection":"exception"} captured with keys: ${C(n)}`}]},extra:{__serialized__:at(n,s)}};if(e){const n=fn(t,e);n.length&&(o.exception.values[0].stacktrace={frames:n})}return o}(t,n,o,f),st(h,{synthetic:!0}),h}return h=pn(t,n,o,c),it(h,`${n}`,void 0),st(h,{synthetic:!0}),h}function pn(t,n,e,r){const i={message:n};if(r&&e){const r=fn(t,e);r.length&&(i.exception={values:[{value:n,stacktrace:{frames:r}}]})}return i}const yn="Breadcrumbs";class mn{static __initStatic(){this.id=yn}__init(){this.name=mn.id}constructor(t){mn.prototype.__init.call(this),this.options={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...t}}setupOnce(){this.options.console&&z("console",vn),this.options.dom&&z("dom",function(t){function n(n){let e,r="object"==typeof t?t.serializeAttribute:void 0;"string"==typeof r&&(r=[r]);try{e=n.event.target?m(n.event.target,r):m(n.event,r)}catch(t){e="<unknown>"}0!==e.length&&Ht().addBreadcrumb({category:`ui.${n.name}`,message:e},{event:n.event,name:n.name,global:n.global})}return n}(this.options.dom)),this.options.xhr&&z("xhr",gn),this.options.fetch&&z("fetch",bn),this.options.history&&z("history",_n)}}function vn(t){const n={category:"console",data:{arguments:t.args,logger:"console"},level:(e=t.level,"warn"===e?"warning":vt.includes(e)?e:"log"),message:x(t.args," ")};var e;if("assert"===t.level){if(!1!==t.args[0])return;n.message=`Assertion failed: ${x(t.args.slice(1)," ")||"console.assert"}`,n.data.arguments=t.args.slice(1)}Ht().addBreadcrumb(n,{input:t.args,level:t.level})}function gn(t){if(t.endTimestamp){if(t.xhr.__sentry_own_request__)return;const{method:n,url:e,status_code:r,body:i}=t.xhr.__sentry_xhr__||{};Ht().addBreadcrumb({category:"xhr",data:{method:n,url:e,status_code:r},type:"http"},{xhr:t.xhr,input:i})}else;}function bn(t){t.endTimestamp&&(t.fetchData.url.match(/sentry_key/)&&"POST"===t.fetchData.method||(t.error?Ht().addBreadcrumb({category:"fetch",data:t.fetchData,level:"error",type:"http"},{data:t.error,input:t.args}):Ht().addBreadcrumb({category:"fetch",data:{...t.fetchData,status_code:t.response.status},type:"http"},{input:t.args,response:t.response})))}function _n(t){let n=t.from,e=t.to;const r=mt(y.location.href);let i=mt(n);const s=mt(e);i.path||(i=r),r.protocol===s.protocol&&r.host===s.host&&(e=s.relative),r.protocol===i.protocol&&r.host===i.host&&(n=i.relative),Ht().addBreadcrumb({category:"navigation",data:{from:n,to:e}})}mn.__initStatic();class wn extends Yt{constructor(t){t.K=t.K||{},t.K.sdk=t.K.sdk||{name:"sentry.javascript.browser",packages:[{name:"npm:@sentry/browser",version:tn}],version:tn},super(t),t.sendClientReports&&y.document&&y.document.addEventListener("visibilitychange",(()=>{"hidden"===y.document.visibilityState&&this.vt()}))}eventFromException(t,n){return function(t,n,e,r){const i=dn(t,n,e&&e.syntheticException||void 0,r);return st(i),i.level="error",e&&e.event_id&&(i.event_id=e.event_id),lt(i)}(this.tt.stackParser,t,n,this.tt.attachStacktrace)}eventFromMessage(t,n="info",e){return function(t,n,e="info",r,i){const s=pn(t,n,r&&r.syntheticException||void 0,i);return s.level=e,r&&r.event_id&&(s.event_id=r.event_id),lt(s)}(this.tt.stackParser,t,n,e,this.tt.attachStacktrace)}sendEvent(t,n){const e=this.getIntegrationById(yn);e&&e.options&&e.options.sentry&&Ht().addBreadcrumb({category:"sentry."+("transaction"===t.type?"transaction":"event"),event_id:t.event_id,level:t.level,message:rt(t)},{event:t}),super.sendEvent(t,n)}at(t,n,e){return t.platform=t.platform||"javascript",super.at(t,n,e)}vt(){const t=this.yt();if(0===t.length)return;if(!this.nt)return;const n=Jt(this.nt,this.tt),e=(r=t,St((i=this.tt.tunnel&&_(this.nt))?{dsn:i}:{},[[{type:"client_report"},{timestamp:s||wt(),discarded_events:r}]]));var r,i,s;try{const t="[object Navigator]"===Object.prototype.toString.call(y&&y.navigator);if(t&&"function"==typeof y.navigator.sendBeacon&&!this.tt.transportOptions){y.navigator.sendBeacon.bind(y.navigator)(n,kt(e))}else this.ct(e)}catch(t){}}}let En;function Sn(t,n=function(){if(En)return En;if(H(y.fetch))return En=y.fetch.bind(y);const t=y.document;let n=y.fetch;if(t&&"function"==typeof t.createElement)try{const e=t.createElement("iframe");e.hidden=!0,t.head.appendChild(e);const r=e.contentWindow;r&&r.fetch&&(n=r.fetch),t.head.removeChild(e)}catch(t){}return En=n.bind(y)}()){return Zt(t,(function(e){const r={body:e.body,method:"POST",referrerPolicy:"origin",headers:t.headers,keepalive:e.body.length<=65536,...t.fetchOptions};return n(t.url,r).then((t=>({statusCode:t.status,headers:{"x-sentry-rate-limits":t.headers.get("X-Sentry-Rate-Limits"),"retry-after":t.headers.get("Retry-After")}})))}))}function $n(t){return Zt(t,(function(n){return new pt(((e,r)=>{const i=new XMLHttpRequest;i.onerror=r,i.onreadystatechange=()=>{4===i.readyState&&e({statusCode:i.status,headers:{"x-sentry-rate-limits":i.getResponseHeader("X-Sentry-Rate-Limits"),"retry-after":i.getResponseHeader("Retry-After")}})},i.open("POST",t.url);for(const n in t.headers)Object.prototype.hasOwnProperty.call(t.headers,n)&&i.setRequestHeader(n,t.headers[n]);i.send(n.body)}))}))}const xn="?";function jn(t,n,e,r){const i={filename:t,function:n,in_app:!0};return void 0!==e&&(i.lineno=e),void 0!==r&&(i.colno=r),i}const kn=/^\s*at (?:(.*\).*?|.*?) ?\((?:address at )?)?((?:file|https?|blob|chrome-extension|address|native|eval|webpack|<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,On=/\((\S*)(?::(\d+))(?::(\d+))\)/,Tn=[30,t=>{const n=kn.exec(t);if(n){if(n[2]&&0===n[2].indexOf("eval")){const t=On.exec(n[2]);t&&(n[2]=t[1],n[3]=t[2],n[4]=t[3])}const[t,e]=Hn(n[1]||xn,n[2]);return jn(e,t,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}}],Dn=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:file|https?|blob|chrome|webpack|resource|moz-extension|safari-extension|safari-web-extension|capacitor)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Rn=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,In=[50,t=>{const n=Dn.exec(t);if(n){if(n[3]&&n[3].indexOf(" > eval")>-1){const t=Rn.exec(n[3]);t&&(n[1]=n[1]||"eval",n[3]=t[1],n[4]=t[2],n[5]="")}let t=n[3],e=n[1]||xn;return[e,t]=Hn(e,t),jn(t,e,n[4]?+n[4]:void 0,n[5]?+n[5]:void 0)}}],Mn=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,Cn=[40,t=>{const n=Mn.exec(t);return n?jn(n[2],n[1]||xn,+n[3],n[4]?+n[4]:void 0):void 0}],Nn=/ line (\d+).*script (?:in )?(\S+)(?:: in function (\S+))?$/i,An=[10,t=>{const n=Nn.exec(t);return n?jn(n[2],n[3]||xn,+n[1]):void 0}],Ln=/ line (\d+), column (\d+)\s*(?:in (?:<anonymous function: ([^>]+)>|([^)]+))\(.*\))? in (.*):\s*$/i,Un=[20,t=>{const n=Ln.exec(t);return n?jn(n[5],n[3]||n[4]||xn,+n[1],+n[2]):void 0}],qn=[Tn,In,Cn],Pn=L(...qn),Hn=(t,n)=>{const e=-1!==t.indexOf("safari-extension"),r=-1!==t.indexOf("safari-web-extension");return e||r?[-1!==t.indexOf("@")?t.split("@")[0]:xn,e?`safari-extension:${n}`:`safari-web-extension:${n}`]:[t,n]};let Fn=0;function Bn(){return Fn>0}function Xn(){Fn+=1,setTimeout((()=>{Fn-=1}))}function zn(t,n={},e){if("function"!=typeof t)return t;try{const n=t.__sentry_wrapped__;if(n)return n;if(D(t))return t}catch(n){return t}const sentryWrapped=function(){const r=Array.prototype.slice.call(arguments);try{e&&"function"==typeof e&&e.apply(this,arguments);const i=r.map((t=>zn(t,n)));return t.apply(this,i)}catch(t){throw Xn(),Xt((e=>{e.addEventProcessor((t=>(n.mechanism&&(it(t,void 0,void 0),st(t,n.mechanism)),t.extra={...t.extra,arguments:r},t))),captureException(t)})),t}};try{for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(sentryWrapped[n]=t[n])}catch(t){}T(sentryWrapped,t),O(t,"__sentry_wrapped__",sentryWrapped);try{Object.getOwnPropertyDescriptor(sentryWrapped,"name").configurable&&Object.defineProperty(sentryWrapped,"name",{get:()=>t.name})}catch(t){}return sentryWrapped}class Wn{static __initStatic(){this.id="GlobalHandlers"}__init(){this.name=Wn.id}__init2(){this.gt={onerror:Jn,onunhandledrejection:Kn}}constructor(t){Wn.prototype.__init.call(this),Wn.prototype.__init2.call(this),this.tt={onerror:!0,onunhandledrejection:!0,...t}}setupOnce(){Error.stackTraceLimit=50;const t=this.tt;for(const n in t){const e=this.gt[n];e&&t[n]&&(e(),this.gt[n]=void 0)}}}function Jn(){z("error",(t=>{const[n,e,r]=Qn();if(!n.getIntegration(Wn))return;const{msg:s,url:c,line:u,column:a,error:f}=t;if(Bn()||f&&f.__sentry_own_request__)return;const h=void 0===f&&o(s)?function(t,n,e,r){const s=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;let o=i(t)?t.message:t,c="Error";const u=o.match(s);u&&(c=u[1],o=u[2]);return Gn({exception:{values:[{type:c,value:o}]}},n,e,r)}(s,c,u,a):Gn(dn(e,f||s,void 0,r,!1),c,u,a);h.level="error",Vn(n,f,h,"onerror")}))}function Kn(){z("unhandledrejection",(t=>{const[n,e,r]=Qn();if(!n.getIntegration(Wn))return;let i=t;try{"reason"in t?i=t.reason:"detail"in t&&"reason"in t.detail&&(i=t.detail.reason)}catch(t){}if(Bn()||i&&i.__sentry_own_request__)return!0;const s=c(i)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(i)}`}]}}:dn(e,i,void 0,r,!0);s.level="error",Vn(n,i,s,"onunhandledrejection")}))}function Gn(t,n,e,r){const i=t.exception=t.exception||{},s=i.values=i.values||[],c=s[0]=s[0]||{},u=c.stacktrace=c.stacktrace||{},a=u.frames=u.frames||[],f=isNaN(parseInt(r,10))?void 0:r,h=isNaN(parseInt(e,10))?void 0:e,l=o(n)&&n.length>0?n:function(){try{return y.document.location.href}catch(t){return""}}();return 0===a.length&&a.push({colno:f,filename:l,function:"?",in_app:!0,lineno:h}),t}function Vn(t,n,e,r){st(e,{handled:!1,type:r}),t.captureEvent(e,{originalException:n})}function Qn(){const t=Ht(),n=t.getClient(),e=n&&n.getOptions()||{stackParser:()=>[],attachStacktrace:!1};return[t,e.stackParser,e.attachStacktrace]}Wn.__initStatic();const Yn=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"];class Zn{static __initStatic(){this.id="TryCatch"}__init(){this.name=Zn.id}constructor(t){Zn.prototype.__init.call(this),this.tt={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...t}}setupOnce(){this.tt.setTimeout&&k(y,"setTimeout",te),this.tt.setInterval&&k(y,"setInterval",te),this.tt.requestAnimationFrame&&k(y,"requestAnimationFrame",ne),this.tt.XMLHttpRequest&&"XMLHttpRequest"in y&&k(XMLHttpRequest.prototype,"send",ee);const t=this.tt.eventTarget;if(t){(Array.isArray(t)?t:Yn).forEach(re)}}}function te(t){return function(...n){const e=n[0];return n[0]=zn(e,{mechanism:{data:{function:q(t)},handled:!0,type:"instrument"}}),t.apply(this,n)}}function ne(t){return function(n){return t.apply(this,[zn(n,{mechanism:{data:{function:"requestAnimationFrame",handler:q(t)},handled:!0,type:"instrument"}})])}}function ee(t){return function(...n){const e=this;return["onload","onerror","onprogress","onreadystatechange"].forEach((t=>{t in e&&"function"==typeof e[t]&&k(e,t,(function(n){const e={mechanism:{data:{function:t,handler:q(n)},handled:!0,type:"instrument"}},r=D(n);return r&&(e.mechanism.data.handler=q(r)),zn(n,e)}))})),t.apply(this,n)}}function re(t){const n=y,e=n[t]&&n[t].prototype;e&&e.hasOwnProperty&&e.hasOwnProperty("addEventListener")&&(k(e,"addEventListener",(function(n){return function(e,r,i){try{"function"==typeof r.handleEvent&&(r.handleEvent=zn(r.handleEvent,{mechanism:{data:{function:"handleEvent",handler:q(r),target:t},handled:!0,type:"instrument"}}))}catch(t){}return n.apply(this,[e,zn(r,{mechanism:{data:{function:"addEventListener",handler:q(r),target:t},handled:!0,type:"instrument"}}),i])}})),k(e,"removeEventListener",(function(t){return function(n,e,r){const i=e;try{const e=i&&i.__sentry_wrapped__;e&&t.call(this,n,e,r)}catch(t){}return t.call(this,n,i,r)}})))}Zn.__initStatic();class ie{static __initStatic(){this.id="LinkedErrors"}__init(){this.name=ie.id}constructor(t={}){ie.prototype.__init.call(this),this.bt=t.key||"cause",this._t=t.limit||5}setupOnce(){const t=Ht().getClient();t&&At(((n,e)=>{const r=Ht().getIntegration(ie);return r?function(t,n,e,r,i){if(!(r.exception&&r.exception.values&&i&&h(i.originalException,Error)))return r;const s=se(t,e,i.originalException,n);return r.exception.values=[...s,...r.exception.values],r}(t.getOptions().stackParser,r.bt,r._t,n,e):n}))}}function se(t,n,e,r,i=[]){if(!h(e[r],Error)||i.length+1>=n)return i;const s=un(t,e[r]);return se(t,n,e[r],r,[s,...i])}ie.__initStatic();class oe{constructor(){oe.prototype.__init.call(this)}static __initStatic(){this.id="HttpContext"}__init(){this.name=oe.id}setupOnce(){At((t=>{if(Ht().getIntegration(oe)){if(!y.navigator&&!y.location&&!y.document)return t;const n=t.request&&t.request.url||y.location&&y.location.href,{referrer:e}=y.document||{},{userAgent:r}=y.navigator||{},i={...n&&{url:n},headers:{...t.request&&t.request.headers,...e&&{Referer:e},...r&&{"User-Agent":r}}};return{...t,request:i}}return t}))}}oe.__initStatic();class ce{constructor(){ce.prototype.__init.call(this)}static __initStatic(){this.id="Dedupe"}__init(){this.name=ce.id}setupOnce(t,n){const e=t=>{const e=n().getIntegration(ce);if(e){try{if(function(t,n){if(!n)return!1;if(function(t,n){const e=t.message,r=n.message;if(!e&&!r)return!1;if(e&&!r||!e&&r)return!1;if(e!==r)return!1;if(!ae(t,n))return!1;if(!ue(t,n))return!1;return!0}(t,n))return!0;if(function(t,n){const e=fe(n),r=fe(t);if(!e||!r)return!1;if(e.type!==r.type||e.value!==r.value)return!1;if(!ae(t,n))return!1;if(!ue(t,n))return!1;return!0}(t,n))return!0;return!1}(t,e.wt))return null}catch(n){return e.wt=t}return e.wt=t}return t};e.id=this.name,t(e)}}function ue(t,n){let e=he(t),r=he(n);if(!e&&!r)return!0;if(e&&!r||!e&&r)return!1;if(e=e,r=r,r.length!==e.length)return!1;for(let t=0;t<r.length;t++){const n=r[t],i=e[t];if(n.filename!==i.filename||n.lineno!==i.lineno||n.colno!==i.colno||n.function!==i.function)return!1}return!0}function ae(t,n){let e=t.fingerprint,r=n.fingerprint;if(!e&&!r)return!0;if(e&&!r||!e&&r)return!1;e=e,r=r;try{return!(e.join("")!==r.join(""))}catch(t){return!1}}function fe(t){return t.exception&&t.exception.values&&t.exception.values[0]}function he(t){const n=t.exception;if(n)try{return n.values[0].stacktrace.frames}catch(t){return}}ce.__initStatic();var le=Object.freeze({__proto__:null,GlobalHandlers:Wn,TryCatch:Zn,Breadcrumbs:mn,LinkedErrors:ie,HttpContext:oe,Dedupe:ce});const de=[new sn,new en,new Zn,new mn,new Wn,new ie,new ce,new oe];function pe(t){t.startSession({ignoreDuration:!0}),t.captureSession()}let ye={};y.Sentry&&y.Sentry.Integrations&&(ye=y.Sentry.Integrations);const me={...ye,...cn,...le};return t.Breadcrumbs=mn,t.BrowserClient=wn,t.Dedupe=ce,t.FunctionToString=en,t.GlobalHandlers=Wn,t.HttpContext=oe,t.Hub=Ut,t.InboundFilters=sn,t.Integrations=me,t.LinkedErrors=ie,t.SDK_VERSION=tn,t.Scope=Ct,t.TryCatch=Zn,t.addBreadcrumb=function(t){Ht().addBreadcrumb(t)},t.addGlobalEventProcessor=At,t.captureEvent=function(t,n){return Ht().captureEvent(t,n)},t.captureException=captureException,t.captureMessage=function(t,n){const e="string"==typeof n?n:void 0,r="string"!=typeof n?{captureContext:n}:void 0;return Ht().captureMessage(t,e,r)},t.chromeStackLineParser=Tn,t.close=function(t){const n=Ht().getClient();return n?n.close(t):lt(!1)},t.configureScope=function(t){Ht().configureScope(t)},t.createTransport=Zt,t.defaultIntegrations=de,t.defaultStackLineParsers=qn,t.defaultStackParser=Pn,t.flush=function(t){const n=Ht().getClient();return n?n.flush(t):lt(!1)},t.forceLoad=function(){},t.geckoStackLineParser=In,t.getCurrentHub=Ht,t.getHubFromCarrier=Ft,t.init=function(t={}){void 0===t.defaultIntegrations&&(t.defaultIntegrations=de),void 0===t.release&&y.SENTRY_RELEASE&&y.SENTRY_RELEASE.id&&(t.release=y.SENTRY_RELEASE.id),void 0===t.autoSessionTracking&&(t.autoSessionTracking=!0),void 0===t.sendClientReports&&(t.sendClientReports=!0);const n={...t,stackParser:(e=t.stackParser||Pn,Array.isArray(e)?L(...e):e),integrations:Qt(t),transport:t.transport||(P()?Sn:$n)};var e;!function(t,n){!0===n.debug&&console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.");const e=Ht(),r=e.getScope();r&&r.update(n.initialScope);const i=new t(n);e.bindClient(i)}(wn,n),t.autoSessionTracking&&function(){if(void 0===y.document)return;const t=Ht();if(!t.captureSession)return;pe(t),z("history",(({from:t,to:n})=>{void 0!==t&&t!==n&&pe(Ht())}))}()},t.lastEventId=function(){return Ht().lastEventId()},t.makeFetchTransport=Sn,t.makeMain=Pt,t.makeXHRTransport=$n,t.onLoad=function(t){t()},t.opera10StackLineParser=An,t.opera11StackLineParser=Un,t.setContext=function(t,n){Ht().setContext(t,n)},t.setExtra=function(t,n){Ht().setExtra(t,n)},t.setExtras=function(t){Ht().setExtras(t)},t.setTag=function(t,n){Ht().setTag(t,n)},t.setTags=function(t){Ht().setTags(t)},t.setUser=function(t){Ht().setUser(t)},t.showReportDialog=function(t={},n=Ht()){if(!y.document)return;const{client:e,scope:r}=n.getStackTop(),i=t.dsn||e&&e.getDsn();if(!i)return;r&&(t.user={...r.getUser(),...t.user}),t.eventId||(t.eventId=n.lastEventId());const s=y.document.createElement("script");s.async=!0,s.src=function(t,n){const e=E(t),r=`${zt(e)}embed/error-page/`;let i=`dsn=${_(e)}`;for(const t in n)if("dsn"!==t)if("user"===t){const t=n.user;if(!t)continue;t.name&&(i+=`&name=${encodeURIComponent(t.name)}`),t.email&&(i+=`&email=${encodeURIComponent(t.email)}`)}else i+=`&${encodeURIComponent(t)}=${encodeURIComponent(n[t])}`;return`${r}?${i}`}(i,t),t.onLoad&&(s.onload=t.onLoad);const o=y.document.head||y.document.body;o&&o.appendChild(s)},t.startTransaction=function(t,n){return Ht().startTransaction({...t},n)},t.winjsStackLineParser=Cn,t.withScope=Xt,t.wrap=function(t){return zn(t)()},t}({});
//# sourceMappingURL=bundle.min.js.map

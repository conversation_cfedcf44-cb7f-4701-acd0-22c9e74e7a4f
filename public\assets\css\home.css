/* Home Page Specific Styles */

/* Enhanced Navigation */
nav {
    backdrop-filter: blur(15px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

nav a {
    position: relative;
    transition: all 0.3s ease;
}

nav a:hover {
    transform: translateY(-2px);
}

nav a.active {
    color: #FFD700;
}

/* Hero Section */
.hero-content {
    animation: fadeInUp 1s ease-out;
}

/* Floating Animation */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes float-delayed {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-15px);
    }
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
    animation: float-delayed 8s ease-in-out infinite;
    animation-delay: 2s;
}

/* Glow Animation for Title */
@keyframes glow {
    0%, 100% {
        text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
    }
    50% {
        text-shadow: 0 0 40px rgba(255, 215, 0, 0.8), 0 0 60px rgba(255, 215, 0, 0.6);
    }
}

.animate-glow {
    animation: glow 3s ease-in-out infinite;
}

/* Feature Cards */
.feature-card {
    transform: translateY(0);
    transition: all 0.4s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
    transition: left 0.5s;
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 215, 0, 0.3);
}

/* Button Animations */
.btn-primary {
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-secondary {
    position: relative;
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
}

/* Background Elements */
.floating-mosque {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

/* Stats Section */
.stats-number {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Responsive Animations */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 3rem;
    }
    
    .feature-card {
        margin-bottom: 1rem;
    }
    
    .floating-mosque {
        display: none;
    }
    
    nav .flex {
        flex-direction: column;
        gap: 1rem;
    }
    
    nav .flex:last-child {
        flex-direction: row;
        gap: 2rem;
    }
}

/* Fade In Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Staggered Animation for Feature Cards */
.feature-card:nth-child(1) {
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.feature-card:nth-child(2) {
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.feature-card:nth-child(3) {
    animation: fadeInUp 0.8s ease-out 0.6s both;
}

/* Enhanced Mosque Logo */
.mosque-logo svg {
    filter: drop-shadow(0 8px 20px rgba(255, 215, 0, 0.3));
    transition: all 0.3s ease;
}

.mosque-logo:hover svg {
    transform: scale(1.05);
    filter: drop-shadow(0 12px 30px rgba(255, 215, 0, 0.5));
}

/* Gradient Text Enhancement */
.bg-clip-text {
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Section Transitions */
section {
    transition: all 0.3s ease;
}

/* Enhanced Backdrop Blur */
.backdrop-blur-md {
    backdrop-filter: blur(12px);
}

.backdrop-blur-sm {
    backdrop-filter: blur(8px);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 215, 0, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 215, 0, 0.7);
}

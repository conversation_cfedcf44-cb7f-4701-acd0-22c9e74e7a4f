# Application Environment
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost

# Database Configuration (if needed in future)
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=prayer_times
DB_USERNAME=root
DB_PASSWORD=

# Email Configuration
MAIL_FROM_EMAIL=<EMAIL>
MAIL_FROM_NAME="Prayer Times App"
MAIL_TO_EMAIL=<EMAIL>

# API Keys (if needed)
ALADHAN_API_URL=https://api.aladhan.com/v1

# Security
SESSION_LIFETIME=120
CSRF_TOKEN_NAME=_token

# Logging
LOG_LEVEL=debug
LOG_FILE=logs/app.log

document.addEventListener('DOMContentLoaded', function() {
    const loadingMessages = [
        'Preparing your prayer times...',
        'Calculating prayer schedules...',
        'Loading location data...',
        'Setting up Qibla direction...',
        'Almost ready...'
    ];
    
    let currentMessageIndex = 0;
    const loadingMessageElement = document.getElementById('loading-message');
    
    // Change loading message every 600ms
    const messageInterval = setInterval(() => {
        currentMessageIndex = (currentMessageIndex + 1) % loadingMessages.length;
        loadingMessageElement.textContent = loadingMessages[currentMessageIndex];
    }, 600);
    
    // Simulate loading time and redirect to main app
    setTimeout(() => {
        clearInterval(messageInterval);
        loadingMessageElement.textContent = 'Ready!';
        
        // Add fade out effect
        document.body.style.transition = 'opacity 0.5s ease-out';
        document.body.style.opacity = '0';
        
        // Redirect to main prayer times app
        setTimeout(() => {
            window.location.href = '/prayer-times';
        }, 500);
    }, 3000); // 3 seconds loading time
    
    // Add some interactive effects
    const logoContainer = document.querySelector('.logo-container');
    logoContainer.addEventListener('click', function() {
        this.style.transform = 'scale(1.1)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 200);
    });
});

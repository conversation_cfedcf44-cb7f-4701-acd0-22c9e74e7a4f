<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - Prayer Times App</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="/assets/css/style.css">
    <link rel="stylesheet" href="/assets/css/contact.css">
</head>
<body class="text-white">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white/10 backdrop-blur-md z-50 border-b border-white/20">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <svg width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 8H22L12 2Z" fill="#FFD700"></path>
                    <path d="M4 10V20H20V10H4Z" fill="#FFD700"></path>
                    <path d="M12 5L5 9H19L12 5Z" fill="#FFD700"></path>
                    <path d="M10 10V20H14V10H10Z" fill="#FFD700"></path>
                    <circle cx="12" cy="6" r="1" fill="#FFD700"></circle>
                </svg>
                <span class="text-lg font-semibold">Prayer Times</span>
            </div>
            <div class="flex space-x-4">
                <a href="/prayer-times" class="text-white hover:text-yellow-300 transition">Home</a>
                <a href="/contact" class="text-yellow-300 font-semibold">Contact</a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8 mt-16">
        <!-- Header -->
        <header class="text-center mb-12">
            <div class="mosque-dome mb-4 inline-block">
                <svg width="60" height="60" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 8H22L12 2Z" fill="#FFD700"></path>
                    <path d="M4 10V20H20V10H4Z" fill="#FFD700"></path>
                    <path d="M12 5L5 9H19L12 5Z" fill="#FFD700"></path>
                    <path d="M10 10V20H14V10H10Z" fill="#FFD700"></path>
                    <circle cx="12" cy="6" r="1" fill="#FFD700"></circle>
                </svg>
            </div>
            <h1 class="text-4xl font-bold mb-4">Contact Us</h1>
            <p class="text-lg opacity-80 max-w-2xl mx-auto">
                We'd love to hear from you! Whether you have questions, suggestions, or need support with the Prayer Times app, feel free to reach out to us.
            </p>
        </header>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
            <!-- Contact Form -->
            <div class="bg-white/10 backdrop-blur-md rounded-xl p-8 shadow-lg contact-form-container">
                <h2 class="text-2xl font-semibold mb-6">Send us a Message</h2>
                <form id="contact-form" class="space-y-6">
                    <div>
                        <label for="name" class="block text-sm font-medium mb-2">Full Name</label>
                        <input 
                            type="text" 
                            id="name" 
                            name="name" 
                            required
                            class="w-full bg-white/20 border border-white/30 rounded-lg px-4 py-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                            placeholder="Enter your full name"
                        >
                    </div>
                    
                    <div>
                        <label for="email" class="block text-sm font-medium mb-2">Email Address</label>
                        <input 
                            type="email" 
                            id="email" 
                            name="email" 
                            required
                            class="w-full bg-white/20 border border-white/30 rounded-lg px-4 py-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                            placeholder="Enter your email address"
                        >
                    </div>
                    
                    <div>
                        <label for="subject" class="block text-sm font-medium mb-2">Subject</label>
                        <select 
                            id="subject" 
                            name="subject" 
                            required
                            class="w-full bg-white/20 border border-white/30 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                        >
                            <option value="">Select a subject</option>
                            <option value="general">General Inquiry</option>
                            <option value="bug">Bug Report</option>
                            <option value="feature">Feature Request</option>
                            <option value="support">Technical Support</option>
                            <option value="feedback">Feedback</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="message" class="block text-sm font-medium mb-2">Message</label>
                        <textarea 
                            id="message" 
                            name="message" 
                            rows="5" 
                            required
                            class="w-full bg-white/20 border border-white/30 rounded-lg px-4 py-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent resize-none"
                            placeholder="Tell us how we can help you..."
                        ></textarea>
                    </div>
                    
                    <button 
                        type="submit" 
                        class="w-full bg-gradient-to-r from-yellow-400 to-yellow-600 text-gray-900 font-semibold py-3 px-6 rounded-lg hover:from-yellow-500 hover:to-yellow-700 transition duration-300 transform hover:scale-105"
                    >
                        Send Message
                    </button>
                </form>
                
                <div id="form-message" class="mt-4 text-center hidden"></div>
            </div>

            <!-- Contact Information -->
            <div class="space-y-6 contact-info-container">
                <!-- Contact Details -->
                <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg">
                    <h3 class="text-xl font-semibold mb-4">Get in Touch</h3>
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-yellow-400 rounded-full flex items-center justify-center">
                                <svg width="20" height="20" fill="currentColor" class="text-gray-900" viewBox="0 0 20 20">
                                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="font-medium">Email</p>
                                <p class="text-sm opacity-80"><EMAIL></p>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-yellow-400 rounded-full flex items-center justify-center">
                                <svg width="20" height="20" fill="currentColor" class="text-gray-900" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="font-medium">Location</p>
                                <p class="text-sm opacity-80">Worldwide Service</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-yellow-400 rounded-full flex items-center justify-center">
                                <svg width="20" height="20" fill="currentColor" class="text-gray-900" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="font-medium">Response Time</p>
                                <p class="text-sm opacity-80">Within 24 hours</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- FAQ Section -->
                <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg">
                    <h3 class="text-xl font-semibold mb-4">Frequently Asked Questions</h3>
                    <div class="space-y-4">
                        <div class="faq-item">
                            <h4 class="font-medium mb-2">How accurate are the prayer times?</h4>
                            <p class="text-sm opacity-80">Our prayer times are calculated using precise astronomical algorithms and are accurate to within minutes for your location.</p>
                        </div>
                        
                        <div class="faq-item">
                            <h4 class="font-medium mb-2">Can I use this app offline?</h4>
                            <p class="text-sm opacity-80">The app requires an internet connection for initial setup and location detection, but prayer times are cached for offline use.</p>
                        </div>
                        
                        <div class="faq-item">
                            <h4 class="font-medium mb-2">How do I change calculation methods?</h4>
                            <p class="text-sm opacity-80">You can change the calculation method in the Settings section on the main page to match your local mosque or preference.</p>
                        </div>
                    </div>
                </div>

                <!-- Social Links -->
                <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg">
                    <h3 class="text-xl font-semibold mb-4">Follow Us</h3>
                    <div class="flex space-x-4">
                        <a href="#" class="social-link w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center hover:bg-blue-700 transition">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M20 10C20 4.477 15.523 0 10 0S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z" clip-rule="evenodd"></path>
                            </svg>
                        </a>
                        <a href="#" class="social-link w-10 h-10 bg-blue-400 rounded-full flex items-center justify-center hover:bg-blue-500 transition">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84"></path>
                            </svg>
                        </a>
                        <a href="#" class="social-link w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center hover:bg-purple-700 transition">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clip-rule="evenodd"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/assets/js/contact.js"></script>
</body>
</html>

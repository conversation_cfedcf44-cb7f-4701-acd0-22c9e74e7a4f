// Prayer Times App - Main JavaScript

let settings = {
    latitude: null,
    longitude: null,
    method: 2, // ISNA
    timeFormat: 12
};

let prayerTimes = {};
let nextPrayer = null;

// Initialize the app
document.addEventListener('DOMContentLoaded', function() {
    loadSettings();
    updateCurrentDate();
    setupEventListeners();
    
    // Try to get location automatically
    if (!settings.latitude || !settings.longitude) {
        getCurrentLocation();
    } else {
        fetchPrayerTimes();
    }
});

function loadSettings() {
    const saved = localStorage.getItem('prayerTimesSettings');
    if (saved) {
        settings = { ...settings, ...JSON.parse(saved) };
        document.getElementById('calculation-method').value = settings.method;
        document.getElementById('time-format').value = settings.timeFormat;
    }
}

function saveSettings() {
    localStorage.setItem('prayerTimesSettings', JSON.stringify(settings));
}

function setupEventListeners() {
    document.getElementById('get-location').addEventListener('click', getCurrentLocation);
    document.getElementById('find-qibla').addEventListener('click', findQiblaDirection);
    document.getElementById('calculation-method').addEventListener('change', function() {
        settings.method = parseInt(this.value);
        saveSettings();
        fetchPrayerTimes();
    });
    document.getElementById('time-format').addEventListener('change', function() {
        settings.timeFormat = parseInt(this.value);
        saveSettings();
        updatePrayerTimesDisplay();
    });
}

function updateCurrentDate() {
    const now = new Date();
    const options = { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    };
    document.getElementById('current-date').textContent = now.toLocaleDateString('en-US', options);
}

function getCurrentLocation() {
    const button = document.getElementById('get-location');
    button.textContent = 'Getting Location...';
    button.disabled = true;
    
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                settings.latitude = position.coords.latitude;
                settings.longitude = position.coords.longitude;
                saveSettings();
                fetchPrayerTimes();
                updateLocationDisplay();
                button.textContent = 'Update Location';
                button.disabled = false;
            },
            function(error) {
                console.error('Error getting location:', error);
                alert('Unable to get your location. Please check your browser settings.');
                button.textContent = 'Update Location';
                button.disabled = false;
            }
        );
    } else {
        alert('Geolocation is not supported by this browser.');
        button.textContent = 'Update Location';
        button.disabled = false;
    }
}

async function fetchPrayerTimes() {
    if (!settings.latitude || !settings.longitude) {
        return;
    }
    
    try {
        const today = new Date();
        const dateString = `${today.getDate()}-${today.getMonth() + 1}-${today.getFullYear()}`;
        
        const response = await fetch(
            `https://api.aladhan.com/v1/timings/${dateString}?latitude=${settings.latitude}&longitude=${settings.longitude}&method=${settings.method}`
        );
        
        if (!response.ok) {
            throw new Error('Failed to fetch prayer times');
        }
        
        const data = await response.json();
        prayerTimes = data.data.timings;
        
        updatePrayerTimesDisplay();
        updateNextPrayer();
        
    } catch (error) {
        console.error('Error fetching prayer times:', error);
        // Use default times if API fails
        setDefaultPrayerTimes();
    }
}

function setDefaultPrayerTimes() {
    prayerTimes = {
        Fajr: '05:30',
        Dhuhr: '12:15',
        Asr: '15:45',
        Maghrib: '18:45',
        Isha: '20:15'
    };
    updatePrayerTimesDisplay();
    updateNextPrayer();
}

function updatePrayerTimesDisplay() {
    const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
    const prayerNames = ['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'];
    
    prayers.forEach((prayer, index) => {
        const timeElement = document.getElementById(`${prayer}-time`);
        if (timeElement && prayerTimes[prayerNames[index]]) {
            timeElement.textContent = formatTimeDisplay(prayerTimes[prayerNames[index]]);
        }
    });
}

function formatTimeDisplay(time24) {
    if (!time24) return '';
    
    const [hours, minutes] = time24.split(':');
    const hour24 = parseInt(hours);
    
    if (settings.timeFormat === 24) {
        return `${hours}:${minutes}`;
    } else {
        const hour12 = hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
        const ampm = hour24 >= 12 ? 'PM' : 'AM';
        return `${hour12}:${minutes} ${ampm}`;
    }
}

function updateNextPrayer() {
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    const prayers = [
        { name: 'Fajr', time: prayerTimes.Fajr },
        { name: 'Dhuhr', time: prayerTimes.Dhuhr },
        { name: 'Asr', time: prayerTimes.Asr },
        { name: 'Maghrib', time: prayerTimes.Maghrib },
        { name: 'Isha', time: prayerTimes.Isha }
    ];
    
    nextPrayer = null;
    
    for (let prayer of prayers) {
        if (prayer.time) {
            const [hours, minutes] = prayer.time.split(':');
            const prayerTime = parseInt(hours) * 60 + parseInt(minutes);
            
            if (prayerTime > currentTime) {
                nextPrayer = { 
                    name: prayer.name, 
                    time: new Date(now) 
                };
                nextPrayer.time.setHours(parseInt(hours));
                nextPrayer.time.setMinutes(parseInt(minutes));
                nextPrayer.time.setSeconds(0);
                break;
            }
        }
    }
    
    // If no next prayer today, it's Fajr tomorrow
    if (!nextPrayer && prayerTimes.Fajr) {
        nextPrayer = { name: 'Fajr (Tomorrow)', time: new Date(now) };
        nextPrayer.time.setDate(nextPrayer.time.getDate() + 1);
        const [hours, minutes] = prayerTimes.Fajr.split(':');
        nextPrayer.time.setHours(parseInt(hours));
        nextPrayer.time.setMinutes(parseInt(minutes));
        nextPrayer.time.setSeconds(0);
    }
    
    if (nextPrayer) {
        document.getElementById('next-prayer-name').textContent = nextPrayer.name;
        document.getElementById('next-prayer-time').textContent = formatTimeDisplay(
            `${nextPrayer.time.getHours().toString().padStart(2, '0')}:${nextPrayer.time.getMinutes().toString().padStart(2, '0')}`
        );
        
        startCountdown();
    }
}

function startCountdown() {
    if (!nextPrayer) return;
    
    const updateCountdown = () => {
        const now = new Date();
        const diff = nextPrayer.time - now;
        
        if (diff <= 0) {
            updateNextPrayer();
            return;
        }
        
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);
        
        document.getElementById('countdown').textContent = 
            `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    };
    
    updateCountdown();
    setInterval(updateCountdown, 1000);
}

function updateLocationDisplay() {
    if (settings.latitude && settings.longitude) {
        document.getElementById('location').textContent = 
            `${settings.latitude.toFixed(2)}, ${settings.longitude.toFixed(2)}`;
    }
}

function findQiblaDirection() {
    if (!settings.latitude || !settings.longitude) {
        alert('Please allow location access first.');
        return;
    }
    
    // Calculate Qibla direction (towards Kaaba in Mecca)
    const meccaLat = 21.4225;
    const meccaLng = 39.8262;
    
    const qiblaAngle = calculateQiblaDirection(settings.latitude, settings.longitude, meccaLat, meccaLng);
    
    document.getElementById('qibla-direction').textContent = `Qibla: ${Math.round(qiblaAngle)}° from North`;
    
    // Rotate compass needle
    const needle = document.querySelector('.compass-needle');
    if (needle) {
        needle.style.transform = `translate(-50%, -50%) rotate(${qiblaAngle}deg)`;
    }
}

function calculateQiblaDirection(lat1, lng1, lat2, lng2) {
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const lat1Rad = lat1 * Math.PI / 180;
    const lat2Rad = lat2 * Math.PI / 180;
    
    const y = Math.sin(dLng) * Math.cos(lat2Rad);
    const x = Math.cos(lat1Rad) * Math.sin(lat2Rad) - Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(dLng);
    
    let bearing = Math.atan2(y, x) * 180 / Math.PI;
    bearing = (bearing + 360) % 360;
    
    return bearing;
}

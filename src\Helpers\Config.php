<?php

namespace PrayerTimes\Helpers;

/**
 * Configuration Helper
 */
class Config
{
    private static $config = null;

    /**
     * Load configuration
     */
    public static function load()
    {
        if (self::$config === null) {
            $configFile = APP_ROOT . '/config/app.php';
            if (file_exists($configFile)) {
                self::$config = require $configFile;
            } else {
                self::$config = [];
            }
        }
        return self::$config;
    }

    /**
     * Get configuration value
     */
    public static function get($key, $default = null)
    {
        $config = self::load();
        $keys = explode('.', $key);
        $value = $config;

        foreach ($keys as $k) {
            if (is_array($value) && isset($value[$k])) {
                $value = $value[$k];
            } else {
                return $default;
            }
        }

        return $value;
    }

    /**
     * Get app name
     */
    public static function getAppName()
    {
        return self::get('name', 'Prayer Times App');
    }

    /**
     * Get app version
     */
    public static function getAppVersion()
    {
        return self::get('version', '1.0.0');
    }

    /**
     * Check if debug mode is enabled
     */
    public static function isDebug()
    {
        return self::get('debug', false);
    }

    /**
     * Get environment
     */
    public static function getEnv()
    {
        return self::get('env', 'production');
    }

    /**
     * Get prayer times configuration
     */
    public static function getPrayerTimesConfig()
    {
        return self::get('prayer_times', []);
    }

    /**
     * Get calculation methods
     */
    public static function getCalculationMethods()
    {
        return self::get('prayer_times.calculation_methods', []);
    }

    /**
     * Get default prayer times
     */
    public static function getDefaultPrayerTimes()
    {
        return self::get('prayer_times.default_times', []);
    }

    /**
     * Get Qibla configuration
     */
    public static function getQiblaConfig()
    {
        return self::get('qibla', []);
    }

    /**
     * Get mail configuration
     */
    public static function getMailConfig()
    {
        return self::get('mail', []);
    }

    /**
     * Get API configuration
     */
    public static function getApiConfig()
    {
        return self::get('api', []);
    }
}

# Prayer Times App

A beautiful, responsive web application for displaying Islamic prayer times with Qibla direction finder.

## Features

- **Loading Page**: Beautiful animated loading screen with mosque logo
- **Prayer Times**: Accurate prayer times based on your location
- **Next Prayer Countdown**: Real-time countdown to the next prayer
- **Qibla Direction**: Find the direction to Mecca from your location
- **Multiple Calculation Methods**: Support for different Islamic calculation methods
- **Responsive Design**: Works perfectly on desktop and mobile devices
- **Contact Page**: Easy way for users to get in touch

## Pages

### 1. Loading Page (`index.html`)
- Animated mosque logo
- Loading progress bar
- Smooth transition to main app
- Responsive design

### 2. Prayer Times (`prayer-times.html`)
- Current prayer times display
- Next prayer countdown
- Qibla direction compass
- Settings for calculation method and time format
- Location-based prayer times

### 3. Contact Page (`contact.html`)
- Contact form with validation
- FAQ section
- Social media links
- Responsive layout

## File Structure

```
prayer-times-app/
├── index.html              # Loading page
├── prayer-times.html       # Main prayer times app
├── contact.html           # Contact page
├── css/
│   ├── loading.css        # Loading page styles
│   ├── style.css          # Main app styles
│   └── contact.css        # Contact page styles
├── js/
│   ├── loading.js         # Loading page functionality
│   ├── main.js           # Main app functionality
│   └── contact.js        # Contact page functionality
└── README.md             # This file
```

## Technologies Used

- **HTML5**: Semantic markup
- **CSS3**: Modern styling with animations and gradients
- **JavaScript**: Vanilla JS for functionality
- **Tailwind CSS**: Utility-first CSS framework
- **Aladhan API**: For accurate prayer times
- **Geolocation API**: For location detection

## Setup Instructions

1. Clone or download the project files
2. Open `index.html` in a web browser
3. Allow location access when prompted for accurate prayer times
4. The app will automatically redirect to the main prayer times page

## API Integration

The app uses the Aladhan API (https://aladhan.com/prayer-times-api) to fetch accurate prayer times based on:
- Geographic coordinates
- Calculation method
- Date

## Browser Compatibility

- Chrome (recommended)
- Firefox
- Safari
- Edge

## Features in Detail

### Prayer Time Calculation
- Supports multiple calculation methods (ISNA, MWL, Egyptian, etc.)
- Automatic location detection
- Manual location update option
- 12/24 hour time format support

### Qibla Direction
- Uses device orientation (where supported)
- Calculates direction to Kaaba in Mecca
- Visual compass interface
- Fallback for devices without orientation support

### Responsive Design
- Mobile-first approach
- Smooth animations and transitions
- Touch-friendly interface
- Optimized for all screen sizes

## Customization

You can customize the app by:
- Modifying colors in CSS files
- Changing calculation methods
- Adding new prayer time sources
- Customizing the loading messages

## License

This project is open source and available under the MIT License.

## Support

For support, please use the contact form in the app or refer to the FAQ section on the contact page.

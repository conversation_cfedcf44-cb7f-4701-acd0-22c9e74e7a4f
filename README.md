# Prayer Times App

A beautiful, responsive PHP web application for displaying Islamic prayer times with Qibla direction finder.

## Features

- **Loading Page**: Beautiful animated loading screen with mosque logo
- **Prayer Times**: Accurate prayer times based on your location
- **Next Prayer Countdown**: Real-time countdown to the next prayer
- **Qibla Direction**: Find the direction to Mecca from your location
- **Multiple Calculation Methods**: Support for different Islamic calculation methods
- **Responsive Design**: Works perfectly on desktop and mobile devices
- **Contact Page**: Easy way for users to get in touch with PHP backend
- **API Endpoints**: RESTful API for prayer times and Qibla direction

## Pages

### 1. Loading Page (`/`)
- Animated mosque logo
- Loading progress bar
- Smooth transition to main app
- Responsive design

### 2. Prayer Times (`/prayer-times`)
- Current prayer times display
- Next prayer countdown
- Qibla direction compass
- Settings for calculation method and time format
- Location-based prayer times

### 3. Contact Page (`/contact`)
- Contact form with validation and PHP backend
- FAQ section
- Social media links
- Responsive layout

## File Structure (PHP Standard)

```
prayer-times-app/
├── public/                 # Web root (Document Root)
│   ├── index.php          # Main entry point
│   ├── .htaccess          # Apache configuration
│   └── assets/            # Static assets
│       ├── css/           # Stylesheets
│       ├── js/            # JavaScript files
│       └── images/        # Images
├── src/                   # PHP source code
│   ├── Controllers/       # MVC Controllers
│   │   ├── HomeController.php
│   │   ├── PrayerTimesController.php
│   │   ├── ContactController.php
│   │   └── ApiController.php
│   ├── Services/          # Business logic
│   │   ├── PrayerTimesService.php
│   │   ├── QiblaService.php
│   │   └── EmailService.php
│   └── Views/             # Template files
│       ├── home/
│       ├── prayer-times/
│       ├── contact/
│       └── errors/
├── config/                # Configuration files
├── logs/                  # Log files
├── vendor/                # Composer dependencies
├── composer.json          # Composer configuration
├── .env.example           # Environment variables template
├── .htaccess             # Root Apache configuration
└── README.md             # This file
```

## Technologies Used

- **PHP 8.0+**: Modern PHP with strict typing
- **Composer**: Dependency management
- **Guzzle HTTP**: HTTP client for API calls
- **Twig**: Template engine (optional)
- **HTML5**: Semantic markup
- **CSS3**: Modern styling with animations and gradients
- **JavaScript**: Vanilla JS for functionality
- **Tailwind CSS**: Utility-first CSS framework
- **Aladhan API**: For accurate prayer times
- **Geolocation API**: For location detection

## Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd prayer-times-app
   ```

2. **Install dependencies**
   ```bash
   composer install
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

4. **Set up web server**
   - Point your web server document root to the `public/` directory
   - Ensure Apache mod_rewrite is enabled
   - For development, you can use PHP's built-in server:
   ```bash
   cd public
   php -S localhost:8000
   ```

5. **Access the application**
   - Open your browser and go to `http://localhost:8000`
   - Allow location access when prompted for accurate prayer times

## API Integration

The app uses the Aladhan API (https://aladhan.com/prayer-times-api) to fetch accurate prayer times based on:
- Geographic coordinates
- Calculation method
- Date

## Browser Compatibility

- Chrome (recommended)
- Firefox
- Safari
- Edge

## Features in Detail

### Prayer Time Calculation
- Supports multiple calculation methods (ISNA, MWL, Egyptian, etc.)
- Automatic location detection
- Manual location update option
- 12/24 hour time format support

### Qibla Direction
- Uses device orientation (where supported)
- Calculates direction to Kaaba in Mecca
- Visual compass interface
- Fallback for devices without orientation support

### Responsive Design
- Mobile-first approach
- Smooth animations and transitions
- Touch-friendly interface
- Optimized for all screen sizes

## Customization

You can customize the app by:
- Modifying colors in CSS files
- Changing calculation methods
- Adding new prayer time sources
- Customizing the loading messages

## License

This project is open source and available under the MIT License.

## Support

For support, please use the contact form in the app or refer to the FAQ section on the contact page.

<?php

/**
 * Application Configuration
 */

return [
    'name' => 'Prayer Times App',
    'version' => '1.0.0',
    'description' => 'Your spiritual companion for accurate prayer times and Islamic guidance',
    
    // Environment
    'env' => $_ENV['APP_ENV'] ?? 'production',
    'debug' => $_ENV['APP_DEBUG'] ?? false,
    'url' => $_ENV['APP_URL'] ?? 'http://localhost:8000',
    
    // Database (for future use)
    'database' => [
        'host' => $_ENV['DB_HOST'] ?? 'localhost',
        'port' => $_ENV['DB_PORT'] ?? 3306,
        'database' => $_ENV['DB_DATABASE'] ?? 'prayer_times',
        'username' => $_ENV['DB_USERNAME'] ?? 'root',
        'password' => $_ENV['DB_PASSWORD'] ?? '',
    ],
    
    // Mail Configuration
    'mail' => [
        'from_email' => $_ENV['MAIL_FROM_EMAIL'] ?? '<EMAIL>',
        'from_name' => $_ENV['MAIL_FROM_NAME'] ?? 'Prayer Times App',
        'to_email' => $_ENV['MAIL_TO_EMAIL'] ?? '<EMAIL>',
    ],
    
    // API Configuration
    'api' => [
        'aladhan_url' => $_ENV['ALADHAN_API_URL'] ?? 'https://api.aladhan.com/v1',
        'timeout' => 10,
    ],
    
    // Prayer Times Configuration
    'prayer_times' => [
        'default_method' => 2, // ISNA
        'calculation_methods' => [
            1 => 'University of Islamic Sciences, Karachi',
            2 => 'Islamic Society of North America (ISNA)',
            3 => 'Muslim World League',
            4 => 'Umm Al-Qura University, Makkah',
            5 => 'Egyptian General Authority of Survey',
            7 => 'Institute of Geophysics, University of Tehran',
            8 => 'Gulf Region',
            9 => 'Kuwait',
            10 => 'Qatar',
            11 => 'Majlis Ugama Islam Singapura, Singapore',
            12 => 'Union Organization islamic de France',
            13 => 'Diyanet İşleri Başkanlığı, Turkey',
            14 => 'Spiritual Administration of Muslims of Russia'
        ],
        'default_times' => [
            'Fajr' => '05:30',
            'Sunrise' => '06:45',
            'Dhuhr' => '12:15',
            'Asr' => '15:45',
            'Sunset' => '18:30',
            'Maghrib' => '18:45',
            'Isha' => '20:15',
            'Imsak' => '05:20',
            'Midnight' => '00:15'
        ]
    ],
    
    // Qibla Configuration
    'qibla' => [
        'kaaba_latitude' => 21.4225,
        'kaaba_longitude' => 39.8262,
    ],
    
    // Security
    'security' => [
        'session_lifetime' => $_ENV['SESSION_LIFETIME'] ?? 120,
        'csrf_token_name' => $_ENV['CSRF_TOKEN_NAME'] ?? '_token',
    ],
    
    // Logging
    'logging' => [
        'level' => $_ENV['LOG_LEVEL'] ?? 'error',
        'file' => $_ENV['LOG_FILE'] ?? 'logs/app.log',
    ],
    
    // Features
    'features' => [
        'contact_form' => true,
        'api_endpoints' => true,
        'location_detection' => true,
        'qibla_direction' => true,
        'multiple_methods' => true,
    ],
    
    // UI Configuration
    'ui' => [
        'theme' => 'default',
        'animations' => true,
        'responsive' => true,
    ]
];

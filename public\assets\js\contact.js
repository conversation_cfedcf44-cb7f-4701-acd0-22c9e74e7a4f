// Contact Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contact-form');
    const formMessage = document.getElementById('form-message');
    
    // Form submission handler
    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();
        handleFormSubmission();
    });
    
    // Add input validation and styling
    setupFormValidation();
    
    // Add FAQ interaction
    setupFAQInteraction();
    
    // Add social link interactions
    setupSocialLinks();
});

function handleFormSubmission() {
    const form = document.getElementById('contact-form');
    const formMessage = document.getElementById('form-message');
    const submitButton = form.querySelector('button[type="submit"]');
    
    // Get form data
    const formData = new FormData(form);
    const data = {
        name: formData.get('name'),
        email: formData.get('email'),
        subject: formData.get('subject'),
        message: formData.get('message')
    };
    
    // Validate form data
    if (!validateFormData(data)) {
        return;
    }
    
    // Show loading state
    form.classList.add('form-loading');
    submitButton.textContent = 'Sending...';
    
    // Send to PHP backend
    fetch('/contact', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showMessage('Thank you for your message! We\'ll get back to you within 24 hours.', 'success');
            form.reset();
        } else {
            showMessage(result.message || 'There was an error sending your message. Please try again.', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('There was an error sending your message. Please try again.', 'error');
    })
    .finally(() => {
        // Remove loading state
        form.classList.remove('form-loading');
        submitButton.textContent = 'Send Message';
    });
}

function validateFormData(data) {
    const formMessage = document.getElementById('form-message');
    
    // Check required fields
    if (!data.name.trim()) {
        showMessage('Please enter your full name.', 'error');
        return false;
    }
    
    if (!data.email.trim()) {
        showMessage('Please enter your email address.', 'error');
        return false;
    }
    
    if (!isValidEmail(data.email)) {
        showMessage('Please enter a valid email address.', 'error');
        return false;
    }
    
    if (!data.subject) {
        showMessage('Please select a subject.', 'error');
        return false;
    }
    
    if (!data.message.trim()) {
        showMessage('Please enter your message.', 'error');
        return false;
    }
    
    if (data.message.trim().length < 10) {
        showMessage('Please enter a message with at least 10 characters.', 'error');
        return false;
    }
    
    return true;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showMessage(message, type) {
    const formMessage = document.getElementById('form-message');
    
    formMessage.textContent = message;
    formMessage.className = `mt-4 text-center message-${type}`;
    formMessage.classList.remove('hidden');
    
    // Hide message after 5 seconds
    setTimeout(() => {
        formMessage.classList.add('hidden');
    }, 5000);
}

function setupFormValidation() {
    const inputs = document.querySelectorAll('input, textarea, select');
    
    inputs.forEach(input => {
        // Real-time validation
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        // Clear validation on focus
        input.addEventListener('focus', function() {
            clearFieldValidation(this);
        });
        
        // Add character counter for message field
        if (input.id === 'message') {
            addCharacterCounter(input);
        }
    });
}

function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let message = '';
    
    switch (field.id) {
        case 'name':
            if (!value) {
                isValid = false;
                message = 'Name is required';
            }
            break;
        case 'email':
            if (!value) {
                isValid = false;
                message = 'Email is required';
            } else if (!isValidEmail(value)) {
                isValid = false;
                message = 'Please enter a valid email';
            }
            break;
        case 'subject':
            if (!value) {
                isValid = false;
                message = 'Please select a subject';
            }
            break;
        case 'message':
            if (!value) {
                isValid = false;
                message = 'Message is required';
            } else if (value.length < 10) {
                isValid = false;
                message = 'Message must be at least 10 characters';
            }
            break;
    }
    
    if (!isValid) {
        showFieldError(field, message);
    } else {
        showFieldSuccess(field);
    }
    
    return isValid;
}

function showFieldError(field, message) {
    field.style.borderColor = '#ef4444';
    field.style.boxShadow = '0 0 0 3px rgba(239, 68, 68, 0.1)';
    
    // Remove existing error message
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
    
    // Add error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error text-red-400 text-sm mt-1';
    errorDiv.textContent = message;
    field.parentNode.appendChild(errorDiv);
}

function showFieldSuccess(field) {
    field.style.borderColor = '#10b981';
    field.style.boxShadow = '0 0 0 3px rgba(16, 185, 129, 0.1)';
    
    // Remove error message
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}

function clearFieldValidation(field) {
    field.style.borderColor = '';
    field.style.boxShadow = '';
    
    // Remove error message
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}

function addCharacterCounter(textarea) {
    const counter = document.createElement('div');
    counter.className = 'text-sm text-white/60 mt-1 text-right';
    counter.textContent = '0 / 500 characters';
    textarea.parentNode.appendChild(counter);
    
    textarea.addEventListener('input', function() {
        const length = this.value.length;
        counter.textContent = `${length} / 500 characters`;
        
        if (length > 500) {
            counter.className = 'text-sm text-red-400 mt-1 text-right';
        } else if (length > 400) {
            counter.className = 'text-sm text-yellow-400 mt-1 text-right';
        } else {
            counter.className = 'text-sm text-white/60 mt-1 text-right';
        }
    });
}

function setupFAQInteraction() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        item.addEventListener('click', function() {
            // Add click animation
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });
}

function setupSocialLinks() {
    const socialLinks = document.querySelectorAll('.social-link');
    
    socialLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Add click animation
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
            
            // Show coming soon message
            showMessage('Social media integration coming soon!', 'success');
        });
    });
}

// Auto-resize textarea
document.addEventListener('input', function(e) {
    if (e.target.tagName === 'TEXTAREA') {
        e.target.style.height = 'auto';
        e.target.style.height = e.target.scrollHeight + 'px';
    }
});

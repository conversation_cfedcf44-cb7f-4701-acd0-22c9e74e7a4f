<?php

namespace PrayerTimes\Services;

/**
 * Qibla Service - Calculates Qibla direction
 */
class QiblaService
{
    // Kaaba coordinates in Mecca
    const KAABA_LATITUDE = 21.4225;
    const KAABA_LONGITUDE = 39.8262;

    /**
     * Calculate Qibla direction from given coordinates
     */
    public function calculateQiblaDirection($latitude, $longitude)
    {
        return $this->calculateBearing(
            $latitude,
            $longitude,
            self::KAABA_LATITUDE,
            self::KAABA_LONGITUDE
        );
    }

    /**
     * Calculate bearing between two points
     */
    private function calculateBearing($lat1, $lng1, $lat2, $lng2)
    {
        // Convert degrees to radians
        $lat1Rad = deg2rad($lat1);
        $lat2Rad = deg2rad($lat2);
        $dLngRad = deg2rad($lng2 - $lng1);

        // Calculate bearing
        $y = sin($dLngRad) * cos($lat2Rad);
        $x = cos($lat1Rad) * sin($lat2Rad) - sin($lat1Rad) * cos($lat2Rad) * cos($dLngRad);

        $bearingRad = atan2($y, $x);
        $bearingDeg = rad2deg($bearingRad);

        // Normalize to 0-360 degrees
        return fmod($bearingDeg + 360, 360);
    }

    /**
     * Get formatted Qibla direction
     */
    public function getFormattedDirection($latitude, $longitude)
    {
        $direction = $this->calculateQiblaDirection($latitude, $longitude);
        return round($direction) . '° from North';
    }

    /**
     * Get compass direction (N, NE, E, SE, S, SW, W, NW)
     */
    public function getCompassDirection($latitude, $longitude)
    {
        $bearing = $this->calculateQiblaDirection($latitude, $longitude);

        $directions = ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'];
        $index = round($bearing / 45) % 8;

        return $directions[$index];
    }
}

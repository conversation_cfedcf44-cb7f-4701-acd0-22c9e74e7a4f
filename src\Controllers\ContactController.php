<?php

namespace PrayerTimes\Controllers;

use PrayerTimes\Services\EmailService;

/**
 * Contact Controller - Handles contact form and page
 */
class ContactController
{
    private $emailService;

    public function __construct()
    {
        $this->emailService = new EmailService();
    }

    public function index()
    {
        $this->render('contact/index');
    }

    public function submit()
    {
        header('Content-Type: application/json');

        try {
            // Get JSON input
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);

            if (!$data) {
                throw new \Exception('Invalid JSON data');
            }

            // Validate input
            $this->validateContactData($data);

            // Sanitize data
            $sanitizedData = $this->sanitizeContactData($data);

            // Send email
            $result = $this->emailService->sendContactEmail($sanitizedData);

            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Thank you for your message! We\'ll get back to you within 24 hours.'
                ]);
            } else {
                throw new \Exception('Failed to send email');
            }

        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    private function validateContactData(array $data): void
    {
        $required = ['name', 'email', 'subject', 'message'];

        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new \Exception("Field '{$field}' is required");
            }
        }

        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            throw new \Exception('Invalid email address');
        }

        if (strlen($data['message']) < 10) {
            throw new \Exception('Message must be at least 10 characters long');
        }

        if (strlen($data['message']) > 5000) {
            throw new \Exception('Message is too long (maximum 5000 characters)');
        }
    }

    private function sanitizeContactData(array $data): array
    {
        return [
            'name' => htmlspecialchars(trim($data['name']), ENT_QUOTES, 'UTF-8'),
            'email' => filter_var(trim($data['email']), FILTER_SANITIZE_EMAIL),
            'subject' => htmlspecialchars(trim($data['subject']), ENT_QUOTES, 'UTF-8'),
            'message' => htmlspecialchars(trim($data['message']), ENT_QUOTES, 'UTF-8')
        ];
    }

    private function render(string $template, array $data = []): void
    {
        extract($data);
        $templatePath = APP_ROOT . '/src/Views/' . $template . '.php';

        if (!file_exists($templatePath)) {
            throw new \Exception("Template not found: {$template}");
        }

        include $templatePath;
    }
}

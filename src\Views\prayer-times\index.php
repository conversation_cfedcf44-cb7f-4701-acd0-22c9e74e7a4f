<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prayer Times App</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="text-white">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white/10 backdrop-blur-md z-50 border-b border-white/20">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <svg width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 8H22L12 2Z" fill="#FFD700"></path>
                    <path d="M4 10V20H20V10H4Z" fill="#FFD700"></path>
                    <path d="M12 5L5 9H19L12 5Z" fill="#FFD700"></path>
                    <path d="M10 10V20H14V10H10Z" fill="#FFD700"></path>
                    <circle cx="12" cy="6" r="1" fill="#FFD700"></circle>
                </svg>
                <span class="text-lg font-semibold">Prayer Times</span>
            </div>
            <div class="flex space-x-6">
                <a href="/" class="text-white hover:text-yellow-300 transition duration-300">Home</a>
                <a href="/prayer-times" class="text-yellow-300 font-semibold border-b-2 border-yellow-300 pb-1">Prayer Times</a>
                <a href="/contact" class="text-white hover:text-yellow-300 transition duration-300">Contact</a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8 mt-16">
        <!-- Header Section -->
        <header class="text-center mb-12">
            <div class="mosque-dome mb-6 animate-float">
                <svg width="100" height="100" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="mx-auto">
                    <path d="M12 2L2 8H22L12 2Z" fill="#FFD700"></path>
                    <path d="M4 10V20H20V10H4Z" fill="#FFD700"></path>
                    <path d="M12 5L5 9H19L12 5Z" fill="#FFD700"></path>
                    <path d="M10 10V20H14V10H10Z" fill="#FFD700"></path>
                    <circle cx="12" cy="6" r="1" fill="#FFD700"></circle>
                </svg>
            </div>
            <h1 class="text-4xl md:text-5xl font-bold text-center mb-4 bg-gradient-to-r from-yellow-400 via-yellow-300 to-yellow-500 bg-clip-text text-transparent">
                Prayer Times
            </h1>
            <div class="space-y-2">
                <p id="current-date" class="text-xl font-medium">Wednesday, May 28, 2025</p>
                <p id="location" class="text-lg opacity-70 flex items-center justify-center">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20" class="mr-2">
                        <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                    </svg>
                    <span id="location-text">Detecting location...</span>
                </p>
            </div>
        </header>

        <!-- Next Prayer Section -->
        <section class="mb-12">
            <div class="next-prayer-card bg-gradient-to-br from-green-500/20 to-blue-600/20 backdrop-blur-md rounded-2xl p-8 shadow-xl border border-white/20">
                <div class="text-center">
                    <div class="flex items-center justify-center mb-4">
                        <svg width="32" height="32" fill="currentColor" viewBox="0 0 20 20" class="text-yellow-400 mr-3">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                        <h2 class="text-2xl font-bold">Next Prayer</h2>
                    </div>

                    <div class="space-y-4">
                        <div>
                            <div class="text-4xl md:text-5xl font-bold text-yellow-300 mb-2" id="next-prayer-name">Maghrib</div>
                            <div class="text-2xl md:text-3xl font-semibold" id="next-prayer-time">6:45 PM</div>
                        </div>

                        <div class="bg-white/10 rounded-xl p-4">
                            <p class="text-lg mb-2 opacity-80">Time remaining</p>
                            <div id="countdown" class="text-3xl md:text-4xl font-mono font-bold text-yellow-300">02:15:30</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Prayer Times Section -->
        <section class="mb-12">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold mb-2">Today's Prayer Times</h2>
                <p class="text-lg opacity-80">Five daily prayers with precise timings</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                <!-- Fajr -->
                <div class="prayer-card group bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg text-center hover:bg-white/20 transition-all duration-300 border border-white/10 hover:border-yellow-400/50">
                    <div class="prayer-icon mb-4">
                        <svg width="40" height="40" fill="currentColor" viewBox="0 0 20 20" class="mx-auto text-yellow-400 group-hover:scale-110 transition-transform">
                            <path d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-yellow-300">Fajr</h3>
                    <div class="text-2xl font-bold mb-2" id="fajr-time">5:30 AM</div>
                    <div class="text-sm opacity-70">Dawn Prayer</div>
                </div>

                <!-- Dhuhr -->
                <div class="prayer-card group bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg text-center hover:bg-white/20 transition-all duration-300 border border-white/10 hover:border-yellow-400/50">
                    <div class="prayer-icon mb-4">
                        <svg width="40" height="40" fill="currentColor" viewBox="0 0 20 20" class="mx-auto text-yellow-400 group-hover:scale-110 transition-transform">
                            <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-yellow-300">Dhuhr</h3>
                    <div class="text-2xl font-bold mb-2" id="dhuhr-time">12:15 PM</div>
                    <div class="text-sm opacity-70">Noon Prayer</div>
                </div>

                <!-- Asr -->
                <div class="prayer-card group bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg text-center hover:bg-white/20 transition-all duration-300 border border-white/10 hover:border-yellow-400/50">
                    <div class="prayer-icon mb-4">
                        <svg width="40" height="40" fill="currentColor" viewBox="0 0 20 20" class="mx-auto text-yellow-400 group-hover:scale-110 transition-transform">
                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-yellow-300">Asr</h3>
                    <div class="text-2xl font-bold mb-2" id="asr-time">3:45 PM</div>
                    <div class="text-sm opacity-70">Afternoon Prayer</div>
                </div>

                <!-- Maghrib -->
                <div class="prayer-card group bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg text-center hover:bg-white/20 transition-all duration-300 border border-white/10 hover:border-yellow-400/50">
                    <div class="prayer-icon mb-4">
                        <svg width="40" height="40" fill="currentColor" viewBox="0 0 20 20" class="mx-auto text-yellow-400 group-hover:scale-110 transition-transform">
                            <path fill-rule="evenodd" d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-yellow-300">Maghrib</h3>
                    <div class="text-2xl font-bold mb-2" id="maghrib-time">6:45 PM</div>
                    <div class="text-sm opacity-70">Sunset Prayer</div>
                </div>

                <!-- Isha -->
                <div class="prayer-card group bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg text-center hover:bg-white/20 transition-all duration-300 border border-white/10 hover:border-yellow-400/50">
                    <div class="prayer-icon mb-4">
                        <svg width="40" height="40" fill="currentColor" viewBox="0 0 20 20" class="mx-auto text-yellow-400 group-hover:scale-110 transition-transform">
                            <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-yellow-300">Isha</h3>
                    <div class="text-2xl font-bold mb-2" id="isha-time">8:15 PM</div>
                    <div class="text-sm opacity-70">Night Prayer</div>
                </div>
            </div>
        </section>

        <!-- Qibla Direction -->
        <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg mb-8">
            <h2 class="text-xl font-semibold mb-4 text-center">Qibla Direction</h2>
            <div class="compass-container">
                <div class="compass bg-white/20 backdrop-blur-md rounded-full border-4 border-white/30">
                    <div class="compass-marking"></div>
                    <div class="compass-marking"></div>
                    <div class="compass-marking"></div>
                    <div class="compass-marking"></div>
                    <div class="compass-needle"></div>
                    <div class="compass-center"></div>
                </div>
            </div>
            <div class="text-center mt-4">
                <p id="qibla-direction" class="text-lg font-medium"></p>
                <button id="find-qibla" class="mt-3 bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition">
                    Find Qibla Direction
                </button>
            </div>
        </div>

        <!-- Settings -->
        <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg">
            <h2 class="text-xl font-semibold mb-4">Settings</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-2">Calculation Method</label>
                    <select id="calculation-method" class="w-full bg-white/20 border border-white/30 rounded-lg px-3 py-2 text-white">
                        <option value="2">Islamic Society of North America (ISNA)</option>
                        <option value="5">Egyptian General Authority of Survey</option>
                        <option value="3">Muslim World League</option>
                        <option value="4">Umm Al-Qura University, Makkah</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Time Format</label>
                    <select id="time-format" class="w-full bg-white/20 border border-white/30 rounded-lg px-3 py-2 text-white">
                        <option value="12">12 Hour</option>
                        <option value="24">24 Hour</option>
                    </select>
                </div>
            </div>
            <button id="get-location" class="mt-4 bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-lg transition">
                Update Location
            </button>
        </div>
    </div>

    <script src="/assets/js/main.js"></script>
</body>
</html>

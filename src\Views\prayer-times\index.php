<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prayer Times App</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="text-white">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white/10 backdrop-blur-md z-50 border-b border-white/20">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <svg width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 8H22L12 2Z" fill="#FFD700"></path>
                    <path d="M4 10V20H20V10H4Z" fill="#FFD700"></path>
                    <path d="M12 5L5 9H19L12 5Z" fill="#FFD700"></path>
                    <path d="M10 10V20H14V10H10Z" fill="#FFD700"></path>
                    <circle cx="12" cy="6" r="1" fill="#FFD700"></circle>
                </svg>
                <span class="text-lg font-semibold">Prayer Times</span>
            </div>
            <div class="flex space-x-4">
                <a href="/prayer-times" class="text-yellow-300 font-semibold">Home</a>
                <a href="/contact" class="text-white hover:text-yellow-300 transition">Contact</a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8 mt-16">
        <header class="flex flex-col items-center mb-8">
            <div class="mosque-dome mb-4">
                <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 8H22L12 2Z" fill="#FFD700"></path>
                    <path d="M4 10V20H20V10H4Z" fill="#FFD700"></path>
                    <path d="M12 5L5 9H19L12 5Z" fill="#FFD700"></path>
                    <path d="M10 10V20H14V10H10Z" fill="#FFD700"></path>
                    <circle cx="12" cy="6" r="1" fill="#FFD700"></circle>
                </svg>
            </div>
            <h1 class="text-3xl font-bold text-center">Prayer Times</h1>
            <p id="current-date" class="text-lg opacity-80 mt-1">Wednesday, May 28, 2025</p>
            <p id="location" class="text-md opacity-70"></p>
        </header>

        <!-- Next Prayer Card -->
        <div class="next-prayer bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg mb-8 text-center">
            <h2 class="text-xl font-semibold mb-2">Next Prayer</h2>
            <div class="text-3xl font-bold mb-2" id="next-prayer-name">Maghrib</div>
            <div class="text-2xl mb-4" id="next-prayer-time">6:45 PM</div>
            <div class="text-lg">
                <span class="opacity-70">Time remaining: </span>
                <span id="countdown" class="font-mono">02:15:30</span>
            </div>
        </div>

        <!-- Prayer Times Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
            <div class="prayer-card bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg text-center">
                <h3 class="text-lg font-semibold mb-2">Fajr</h3>
                <div class="text-2xl font-bold" id="fajr-time">5:30 AM</div>
                <div class="text-sm opacity-70 mt-1">Dawn Prayer</div>
            </div>
            
            <div class="prayer-card bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg text-center">
                <h3 class="text-lg font-semibold mb-2">Dhuhr</h3>
                <div class="text-2xl font-bold" id="dhuhr-time">12:15 PM</div>
                <div class="text-sm opacity-70 mt-1">Noon Prayer</div>
            </div>
            
            <div class="prayer-card bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg text-center">
                <h3 class="text-lg font-semibold mb-2">Asr</h3>
                <div class="text-2xl font-bold" id="asr-time">3:45 PM</div>
                <div class="text-sm opacity-70 mt-1">Afternoon Prayer</div>
            </div>
            
            <div class="prayer-card bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg text-center">
                <h3 class="text-lg font-semibold mb-2">Maghrib</h3>
                <div class="text-2xl font-bold" id="maghrib-time">6:45 PM</div>
                <div class="text-sm opacity-70 mt-1">Sunset Prayer</div>
            </div>
            
            <div class="prayer-card bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg text-center">
                <h3 class="text-lg font-semibold mb-2">Isha</h3>
                <div class="text-2xl font-bold" id="isha-time">8:15 PM</div>
                <div class="text-sm opacity-70 mt-1">Night Prayer</div>
            </div>
        </div>

        <!-- Qibla Direction -->
        <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg mb-8">
            <h2 class="text-xl font-semibold mb-4 text-center">Qibla Direction</h2>
            <div class="compass-container">
                <div class="compass bg-white/20 backdrop-blur-md rounded-full border-4 border-white/30">
                    <div class="compass-marking"></div>
                    <div class="compass-marking"></div>
                    <div class="compass-marking"></div>
                    <div class="compass-marking"></div>
                    <div class="compass-needle"></div>
                    <div class="compass-center"></div>
                </div>
            </div>
            <div class="text-center mt-4">
                <p id="qibla-direction" class="text-lg font-medium"></p>
                <button id="find-qibla" class="mt-3 bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition">
                    Find Qibla Direction
                </button>
            </div>
        </div>

        <!-- Settings -->
        <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 shadow-lg">
            <h2 class="text-xl font-semibold mb-4">Settings</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-2">Calculation Method</label>
                    <select id="calculation-method" class="w-full bg-white/20 border border-white/30 rounded-lg px-3 py-2 text-white">
                        <option value="2">Islamic Society of North America (ISNA)</option>
                        <option value="5">Egyptian General Authority of Survey</option>
                        <option value="3">Muslim World League</option>
                        <option value="4">Umm Al-Qura University, Makkah</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Time Format</label>
                    <select id="time-format" class="w-full bg-white/20 border border-white/30 rounded-lg px-3 py-2 text-white">
                        <option value="12">12 Hour</option>
                        <option value="24">24 Hour</option>
                    </select>
                </div>
            </div>
            <button id="get-location" class="mt-4 bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-lg transition">
                Update Location
            </button>
        </div>
    </div>

    <script src="/assets/js/main.js"></script>
</body>
</html>

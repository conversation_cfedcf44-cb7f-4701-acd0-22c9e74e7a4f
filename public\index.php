<?php

/**
 * Prayer Times App - Main Entry Point
 *
 * This is the main entry point for the Prayer Times application.
 * All requests are routed through this file.
 */

// Start session
session_start();

// Set error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', '1');

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);

// Load Composer autoloader
require_once APP_ROOT . '/vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(APP_ROOT);
if (file_exists(APP_ROOT . '/.env')) {
    $dotenv->load();
}

use PrayerTimes\Controllers\HomeController;
use PrayerTimes\Controllers\PrayerTimesController;
use PrayerTimes\Controllers\ContactController;
use PrayerTimes\Controllers\ApiController;

// Simple router
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);
$method = $_SERVER['REQUEST_METHOD'];

// Remove query string and normalize path
$path = strtok($path, '?');
$path = rtrim($path, '/') ?: '/';

try {
    // Route handling
    switch ($path) {
        case '/':
            $controller = new HomeController();
            $controller->index();
            break;

        case '/prayer-times':
            $controller = new PrayerTimesController();
            $controller->index();
            break;

        case '/contact':
            $controller = new ContactController();
            if ($method === 'POST') {
                $controller->submit();
            } else {
                $controller->index();
            }
            break;

        case '/api/prayer-times':
            $controller = new ApiController();
            $controller->getPrayerTimes();
            break;

        case '/api/qibla':
            $controller = new ApiController();
            $controller->getQiblaDirection();
            break;

        default:
            // Check if it's a static asset
            if (preg_match('/\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf)$/', $path)) {
                // Let the web server handle static files
                return false;
            }

            // 404 Not Found
            http_response_code(404);
            include APP_ROOT . '/src/Views/errors/404.php';
            break;
    }
} catch (Exception $e) {
    // Error handling
    error_log($e->getMessage());

    if ($_ENV['APP_ENV'] ?? 'production' === 'development') {
        echo '<h1>Error</h1>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    } else {
        http_response_code(500);
        include APP_ROOT . '/src/Views/errors/500.php';
    }
}

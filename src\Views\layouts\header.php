<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $title ?? 'Prayer Times App'; ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="/assets/css/style.css">
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link rel="stylesheet" href="<?php echo $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body class="text-white">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white/10 backdrop-blur-md z-50 border-b border-white/20">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <svg width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 8H22L12 2Z" fill="#FFD700"></path>
                    <path d="M4 10V20H20V10H4Z" fill="#FFD700"></path>
                    <path d="M12 5L5 9H19L12 5Z" fill="#FFD700"></path>
                    <path d="M10 10V20H14V10H10Z" fill="#FFD700"></path>
                    <circle cx="12" cy="6" r="1" fill="#FFD700"></circle>
                </svg>
                <span class="text-lg font-semibold">Prayer Times</span>
            </div>
            <div class="flex space-x-6">
                <a href="/" class="nav-link <?php echo ($currentPage ?? '') === 'home' ? 'active' : ''; ?>">Home</a>
                <a href="/prayer-times" class="nav-link <?php echo ($currentPage ?? '') === 'prayer-times' ? 'active' : ''; ?>">Prayer Times</a>
                <a href="/contact" class="nav-link <?php echo ($currentPage ?? '') === 'contact' ? 'active' : ''; ?>">Contact</a>
            </div>
        </div>
    </nav>

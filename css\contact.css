/* Contact Page Specific Styles */

.contact-form-container {
    animation: slideInLeft 0.8s ease-out;
}

.contact-info-container {
    animation: slideInRight 0.8s ease-out;
}

/* Form Styles */
input, textarea, select {
    transition: all 0.3s ease;
}

input:focus, textarea:focus, select:focus {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.2);
}

/* Button Hover Effects */
button[type="submit"] {
    position: relative;
    overflow: hidden;
}

button[type="submit"]:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

button[type="submit"]:hover:before {
    left: 100%;
}

/* FAQ Accordion Effect */
.faq-item {
    cursor: pointer;
    transition: all 0.3s ease;
}

.faq-item:hover {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 0.5rem;
    margin: -0.5rem;
}

/* Social Links Animation */
.social-link {
    transition: all 0.3s ease;
}

.social-link:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* Success/Error Message Styles */
.message-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid rgba(16, 185, 129, 0.3);
    animation: slideInUp 0.5s ease-out;
}

.message-error {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid rgba(239, 68, 68, 0.3);
    animation: slideInUp 0.5s ease-out;
}

/* Loading State */
.form-loading {
    opacity: 0.6;
    pointer-events: none;
}

.form-loading button[type="submit"] {
    position: relative;
}

.form-loading button[type="submit"]:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
}

/* Animations */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-form-container,
    .contact-info-container {
        animation: slideInUp 0.8s ease-out;
    }
    
    .grid {
        gap: 2rem;
    }
    
    .social-link {
        margin: 0.25rem;
    }
}

/* Custom Scrollbar for Textarea */
textarea::-webkit-scrollbar {
    width: 8px;
}

textarea::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

textarea::-webkit-scrollbar-thumb {
    background: rgba(255, 215, 0, 0.5);
    border-radius: 4px;
}

textarea::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 215, 0, 0.7);
}

/* Placeholder Animation */
input::placeholder,
textarea::placeholder {
    transition: all 0.3s ease;
}

input:focus::placeholder,
textarea:focus::placeholder {
    opacity: 0.5;
    transform: translateY(-2px);
}

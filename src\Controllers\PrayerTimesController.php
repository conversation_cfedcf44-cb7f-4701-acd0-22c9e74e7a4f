<?php

namespace PrayerTimes\Controllers;

/**
 * Prayer Times Controller - Handles the main prayer times page
 */
class PrayerTimesController
{
    public function index()
    {
        $this->render('prayer-times/index');
    }

    private function render($template, $data = [])
    {
        extract($data);
        $templatePath = APP_ROOT . '/src/Views/' . $template . '.php';

        if (!file_exists($templatePath)) {
            throw new \Exception("Template not found: {$template}");
        }

        include $templatePath;
    }
}

{"name": "prayer-times/app", "description": "A beautiful, responsive web application for displaying Islamic prayer times with Qibla direction finder", "type": "project", "license": "MIT", "authors": [{"name": "Prayer Times App", "email": "<EMAIL>"}], "require": {"php": ">=7.3", "ext-json": "*", "ext-curl": "*", "guzzlehttp/guzzle": "^7.0", "twig/twig": "^3.0", "vlucas/phpdotenv": "^5.0"}, "require-dev": {"phpunit/phpunit": "^9.0", "phpstan/phpstan": "^1.0", "squizlabs/php_codesniffer": "^3.0"}, "autoload": {"psr-4": {"PrayerTimes\\": "src/"}}, "autoload-dev": {"psr-4": {"PrayerTimes\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "phpstan": "phpstan analyse src", "phpcs": "phpcs src --standard=PSR12", "phpcbf": "phpcbf src --standard=PSR12"}, "config": {"optimize-autoloader": true, "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}
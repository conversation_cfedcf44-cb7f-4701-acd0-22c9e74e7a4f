<?php

namespace PrayerTimes\Controllers;

use PrayerTimes\Services\PrayerTimesService;
use PrayerTimes\Services\QiblaService;

/**
 * API Controller - Handles API endpoints
 */
class ApiController
{
    private PrayerTimesService $prayerTimesService;
    private QiblaService $qiblaService;

    public function __construct()
    {
        $this->prayerTimesService = new PrayerTimesService();
        $this->qiblaService = new QiblaService();
    }

    public function getPrayerTimes(): void
    {
        header('Content-Type: application/json');

        try {
            $latitude = $_GET['latitude'] ?? null;
            $longitude = $_GET['longitude'] ?? null;
            $method = $_GET['method'] ?? 2;
            $date = $_GET['date'] ?? date('d-m-Y');

            if (!$latitude || !$longitude) {
                throw new \Exception('Latitude and longitude are required');
            }

            $prayerTimes = $this->prayerTimesService->getPrayerTimes(
                (float) $latitude,
                (float) $longitude,
                (int) $method,
                $date
            );

            echo json_encode([
                'success' => true,
                'data' => $prayerTimes
            ]);

        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function getQiblaDirection(): void
    {
        header('Content-Type: application/json');

        try {
            $latitude = $_GET['latitude'] ?? null;
            $longitude = $_GET['longitude'] ?? null;

            if (!$latitude || !$longitude) {
                throw new \Exception('Latitude and longitude are required');
            }

            $direction = $this->qiblaService->calculateQiblaDirection(
                (float) $latitude,
                (float) $longitude
            );

            echo json_encode([
                'success' => true,
                'data' => [
                    'direction' => $direction,
                    'formatted' => round($direction) . '° from North'
                ]
            ]);

        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}
